const { testConnection, initializeTables } = require('../config/database');

async function initializeDatabase() {
  console.log('🔄 Initializing NRAA Rankings Database...');
  
  try {
    // Test database connection
    console.log('📡 Testing database connection...');
    const connected = await testConnection();
    
    if (!connected) {
      console.error('❌ Database connection failed. Please check your configuration.');
      process.exit(1);
    }
    
    // Initialize tables
    console.log('🏗️  Creating database tables...');
    await initializeTables();
    
    console.log('✅ Database initialization completed successfully!');
    console.log('');
    console.log('📊 Database structure:');
    console.log('  • events - Competition events');
    console.log('  • shooters - Shooter information');
    console.log('  • results - Competition results');
    console.log('  • shooter_rankings - Ranking view');
    console.log('');
    console.log('🚀 You can now start the API server with: npm start');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);
    process.exit(1);
  }
  
  process.exit(0);
}

// Run initialization
initializeDatabase();
