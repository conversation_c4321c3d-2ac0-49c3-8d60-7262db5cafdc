const express = require('express');
const Joi = require('joi');
const { executeQuery, getConnection, normalizeShooterName, calculatePoints } = require('../config/database');

const router = express.Router();

// Validation schema for bulk import
const bulkImportSchema = Joi.object({
  events: Joi.array().items(
    Joi.object({
      date: Joi.date().required(),
      location: Joi.string().max(50).required(),
      aGradeCount: Joi.number().integer().min(1).required(),
      competitionName: Joi.string().max(255).optional(),
      sourceUrl: Joi.string().uri().optional(),
      results: Joi.array().items(
        Joi.object({
          shooter: Joi.string().max(255).required(),
          position: Joi.number().integer().min(1).required(),
          score: Joi.number().optional()
        })
      ).min(1).required()
    })
  ).min(1).required()
});

// POST /api/import/bulk - Bulk import events
router.post('/bulk', async (req, res) => {
  const connection = await getConnection();
  
  try {
    // Validate request body
    const { error, value } = bulkImportSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        details: error.details.map(d => d.message)
      });
    }
    
    const { events } = value;
    
    await connection.beginTransaction();
    
    const importResults = {
      imported: 0,
      skipped: 0,
      errors: 0,
      details: []
    };
    
    for (const eventData of events) {
      try {
        const { date, location, aGradeCount, competitionName, sourceUrl, results } = eventData;
        
        // Check for duplicate event
        const existingEvents = await connection.execute(
          'SELECT id FROM events WHERE date = ? AND location = ? AND a_grade_count = ?',
          [date, location, aGradeCount]
        );
        
        if (existingEvents[0].length > 0) {
          importResults.skipped++;
          importResults.details.push(`Skipped: ${location} ${date} (already exists)`);
          continue;
        }
        
        // Create event
        const [eventResult] = await connection.execute(`
          INSERT INTO events (date, location, a_grade_count, competition_name, source_url)
          VALUES (?, ?, ?, ?, ?)
        `, [date, location, aGradeCount, competitionName, sourceUrl]);
        
        const eventId = eventResult.insertId;
        
        // Process shooters and results
        for (const result of results) {
          // Get or create shooter
          const normalizedName = normalizeShooterName(result.shooter);
          
          let shooterId;
          const existingShooters = await connection.execute(
            'SELECT id FROM shooters WHERE normalized_name = ?',
            [normalizedName]
          );
          
          if (existingShooters[0].length > 0) {
            shooterId = existingShooters[0][0].id;
          } else {
            const [shooterResult] = await connection.execute(
              'INSERT INTO shooters (name, normalized_name) VALUES (?, ?)',
              [result.shooter, normalizedName]
            );
            shooterId = shooterResult.insertId;
          }
          
          // Calculate points
          const points = calculatePoints(result.position, aGradeCount);
          
          // Insert result
          await connection.execute(`
            INSERT INTO results (event_id, shooter_id, position, score, points)
            VALUES (?, ?, ?, ?, ?)
          `, [eventId, shooterId, result.position, result.score || null, points]);
        }
        
        importResults.imported++;
        importResults.details.push(`Imported: ${location} ${date} (${results.length} shooters)`);
        
      } catch (eventError) {
        importResults.errors++;
        importResults.details.push(`Error: ${eventData.location} ${eventData.date} - ${eventError.message}`);
        console.error('Event import error:', eventError);
      }
    }
    
    await connection.commit();
    
    res.json({
      message: 'Bulk import completed',
      results: importResults
    });
    
  } catch (error) {
    await connection.rollback();
    console.error('Error in bulk import:', error);
    res.status(500).json({ error: 'Bulk import failed' });
  } finally {
    connection.release();
  }
});

// POST /api/import/validate - Validate import data without importing
router.post('/validate', async (req, res) => {
  try {
    // Validate request body
    const { error, value } = bulkImportSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        details: error.details.map(d => d.message)
      });
    }
    
    const { events } = value;
    
    const validationResults = {
      valid: 0,
      duplicates: 0,
      errors: 0,
      details: []
    };
    
    for (const eventData of events) {
      try {
        const { date, location, aGradeCount, results } = eventData;
        
        // Check for duplicate event
        const existingEvents = await executeQuery(
          'SELECT id FROM events WHERE date = ? AND location = ? AND a_grade_count = ?',
          [date, location, aGradeCount]
        );
        
        if (existingEvents.length > 0) {
          validationResults.duplicates++;
          validationResults.details.push(`Duplicate: ${location} ${date}`);
          continue;
        }
        
        // Validate results structure
        const positions = results.map(r => r.position);
        const uniquePositions = new Set(positions);
        
        if (positions.length !== uniquePositions.size) {
          validationResults.errors++;
          validationResults.details.push(`Error: ${location} ${date} - Duplicate positions`);
          continue;
        }
        
        const shooterNames = results.map(r => normalizeShooterName(r.shooter));
        const uniqueShooters = new Set(shooterNames);
        
        if (shooterNames.length !== uniqueShooters.size) {
          validationResults.errors++;
          validationResults.details.push(`Error: ${location} ${date} - Duplicate shooters`);
          continue;
        }
        
        validationResults.valid++;
        validationResults.details.push(`Valid: ${location} ${date} (${results.length} shooters)`);
        
      } catch (eventError) {
        validationResults.errors++;
        validationResults.details.push(`Error: ${eventData.location} ${eventData.date} - ${eventError.message}`);
      }
    }
    
    res.json({
      message: 'Validation completed',
      results: validationResults
    });
    
  } catch (error) {
    console.error('Error in validation:', error);
    res.status(500).json({ error: 'Validation failed' });
  }
});

// GET /api/import/stats - Get import statistics
router.get('/stats', async (req, res) => {
  try {
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total_events,
        COUNT(DISTINCT location) as total_locations,
        MIN(date) as earliest_event,
        MAX(date) as latest_event,
        SUM(a_grade_count) as total_participants
      FROM events
    `);
    
    const shooterStats = await executeQuery(`
      SELECT COUNT(*) as total_shooters
      FROM shooters
    `);
    
    const resultStats = await executeQuery(`
      SELECT COUNT(*) as total_results
      FROM results
    `);
    
    res.json({
      events: stats[0],
      shooters: shooterStats[0],
      results: resultStats[0]
    });
  } catch (error) {
    console.error('Error fetching import stats:', error);
    res.status(500).json({ error: 'Failed to fetch import statistics' });
  }
});

module.exports = router;
