<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shooter Rankings - Kings Prize System</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <h1>🎯 NRAA Rankings System</h1>
        <p>Track and rank NRAA competition results with automatic point calculation</p>
        <div id="backend-status" class="backend-status">🔄 Connecting...</div>
    </header>
    
    <main>
        <!-- Navigation Tabs -->
        <div class="tab-container">
            <button class="tab-btn active" data-tab="rankings">Rankings</button>
            <button class="tab-btn" data-tab="score-entry">Score Entry</button>
            <button class="tab-btn" data-tab="events">Events</button>
            <button class="tab-btn" data-tab="data-import">Data Import</button>
        </div>

        <!-- Rankings Tab -->
        <div id="rankings-tab" class="tab-content active">
            <div class="controls">
                <button id="refresh-btn">Refresh Rankings</button>
                <input type="text" id="search" placeholder="Search shooter...">
            </div>

            <div class="rankings-table">
                <table id="rankings">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Shooter</th>
                            <th>Total Points</th>
                            <th>Events</th>
                            <th>Best Result</th>
                        </tr>
                    </thead>
                    <tbody id="rankings-body">
                        <!-- Rankings will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Score Entry Tab -->
        <div id="score-entry-tab" class="tab-content">
            <div class="score-entry-container">
                <h2>Enter Event Scores</h2>

                <!-- Event Information Form -->
                <div class="event-form">
                    <h3>Event Information</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="event-date">Event Date:</label>
                            <input type="date" id="event-date" required>
                        </div>
                        <div class="form-group">
                            <label for="event-location">Location:</label>
                            <select id="event-location" required>
                                <option value="">Select Location</option>
                                <option value="NSW">NSW</option>
                                <option value="VIC">VIC</option>
                                <option value="QLD">QLD</option>
                                <option value="SA">SA</option>
                                <option value="WA">WA</option>
                                <option value="TAS">TAS</option>
                                <option value="NT">NT</option>
                                <option value="ACT">ACT</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="a-grade-count">A Grade Count:</label>
                            <input type="number" id="a-grade-count" min="1" required>
                        </div>
                    </div>
                </div>

                <!-- Shooter Results Form -->
                <div class="results-form">
                    <h3>Shooter Results</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="shooter-name">Shooter Name:</label>
                            <input type="text" id="shooter-name" placeholder="Enter shooter name" list="shooter-suggestions">
                            <datalist id="shooter-suggestions"></datalist>
                        </div>
                        <div class="form-group">
                            <label for="shooter-position">Position:</label>
                            <div class="position-input-group">
                                <input type="number" id="shooter-position" min="1" placeholder="Final position">
                                <button type="button" id="auto-increment-btn" class="auto-increment-btn active" title="Auto-increment positions">🔢 Auto</button>
                            </div>
                        </div>
                        <div class="form-group">
                            <button type="button" id="add-shooter-btn">Add Shooter</button>
                        </div>
                    </div>
                </div>

                <!-- Current Event Results -->
                <div class="current-results">
                    <h3>Current Event Results</h3>
                    <div class="results-list" id="current-results-list">
                        <p class="no-results">No shooters added yet</p>
                    </div>
                    <div class="form-actions">
                        <button type="button" id="save-event-btn" disabled>Save Event</button>
                        <button type="button" id="clear-event-btn">Clear All</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Events Tab -->
        <div id="events-tab" class="tab-content">
            <div class="events-container">
                <h2>Event History</h2>
                <div class="events-list" id="events-list">
                    <!-- Events will be populated here -->
                </div>
            </div>
        </div>

        <!-- Data Import Tab -->
        <div id="data-import-tab" class="tab-content">
            <div class="score-entry-container">
                <h2>Data Import</h2>

                <!-- NRAA Auto-Scraper -->
                <div class="scraper-controls">
                    <h3>🌐 NRAA Results Auto-Scraper</h3>
                    <p>Automatically import Kings Prize results from the official NRAA website.</p>

                    <div class="scraper-buttons">
                        <button class="scraper-btn" onclick="scraperIntegration.scrapeAndImportResults()">
                            🚀 Import All Years (2023-2025)
                        </button>
                        <button class="scraper-btn test" onclick="testScrapingFunction()">
                            🧪 Test Import (Sample Data)
                        </button>
                        <button class="scraper-btn" onclick="showAllNRAAUrls()" style="background-color: #28a745;">
                            🔗 Show All URLs
                        </button>
                        <button class="scraper-btn" onclick="showPasteImport()" style="background-color: #ffc107; color: #212529;">
                            📋 Paste Data
                        </button>
                        <button class="scraper-btn" onclick="showAllYearDiscovery()" style="background-color: #9c27b0; color: white;">
                            🔍 Discover URLs
                        </button>
                    </div>

                    <div style="margin-top: 15px;">
                        <label for="specific-url">Import from specific URL:</label>
                        <div class="url-input-group">
                            <input type="url" id="specific-url" placeholder="https://www.results.nraa.com.au/nqra-kings-2024-results/">
                            <button class="scraper-btn" onclick="scrapeSpecificUrlFunction()">Import URL</button>
                        </div>
                    </div>

                    <div style="margin-top: 15px; font-size: 14px; color: #666;">
                        <strong>Supported URLs:</strong>
                        <ul style="margin: 5px 0; padding-left: 20px;">
                            <li>Year pages: /all-kings-prize-results-YYYY/</li>
                            <li>State competitions: /[state]-kings-YYYY-results/</li>
                        </ul>
                        <strong>Note:</strong> The scraper will automatically detect and import Kings Aggregate results only.
                    </div>
                </div>

                <!-- Manual Data Import -->
                <div class="scraper-controls">
                    <h3>📁 Manual Data Import</h3>
                    <p>Import data from JSON backup files or manual data entry.</p>

                    <div class="scraper-buttons">
                        <button class="scraper-btn" onclick="triggerFileImport()">
                            📂 Import from JSON File
                        </button>
                        <button class="scraper-btn" onclick="exportData()">
                            💾 Export Current Data
                        </button>
                        <button class="scraper-btn" onclick="showStatistics()">
                            📊 View Statistics
                        </button>
                    </div>

                    <input type="file" id="json-file-input" accept=".json" style="display: none;" onchange="handleFileImport(event)">
                </div>

                <!-- Import History -->
                <div class="scraper-controls">
                    <h3>📋 Import History</h3>
                    <div id="import-history" style="max-height: 200px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 4px;">
                        <p style="color: #666; font-style: italic;">No imports performed yet.</p>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <script src="api-client.js"></script>
    <script src="backend-integration.js"></script>
    <script src="ranking-system.js"></script>
    <script src="data-manager.js"></script>
    <script src="nraa-scraper.js"></script>
    <script src="scraper-integration.js"></script>
    <script src="import-helpers.js"></script>
    <script src="manual-import-helper.js"></script>
    <script src="url-discovery.js"></script>
</body>
</html>

