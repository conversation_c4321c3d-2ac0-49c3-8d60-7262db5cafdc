version: '3.8'

services:
  # Frontend (Nginx serving static files)
  web:
    build: .
    ports:
      - "8080:80"
    volumes:
      # Mount current directory for live development
      - .:/usr/share/nginx/html:ro
    depends_on:
      - api
    restart: unless-stopped
    container_name: nraa_emms_grading_web
    networks:
      - nraa-network

  # Backend API (Node.js)
  api:
    build: ./backend
    ports:
      - "3001:3001"
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=nraa_user
      - DB_PASSWORD=nraa_password
      - DB_NAME=nraa_rankings
      - NODE_ENV=production
      - FRONTEND_URL=http://localhost:8080
    depends_on:
      mysql:
        condition: service_healthy
    restart: unless-stopped
    container_name: nraa_emms_grading_api
    networks:
      - nraa-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3001/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL Database
  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=nraa_rankings
      - MYSQL_USER=nraa_user
      - MYSQL_PASSWORD=nraa_password
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped
    container_name: nraa_emms_grading_mysql
    networks:
      - nraa-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "nraa_user", "-pnraa_password"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Optional: Add a development server with hot reload
  dev:
    image: node:18-alpine
    working_dir: /app
    volumes:
      - .:/app
    ports:
      - "3000:3000"
    command: npx http-server -p 3000 -c-1
    container_name: nraa_emms_grading_dev
    networks:
      - nraa-network
    profiles:
      - dev

networks:
  nraa-network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
