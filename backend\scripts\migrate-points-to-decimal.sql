-- Migration script to update points column from INT to DECIMAL(15,10)
-- This script updates existing databases to support the new points calculation system

USE nraa_rankings;

-- Alter the results table to change points column to DECIMAL
ALTER TABLE results MODIFY COLUMN points DECIMAL(15,10) NOT NULL DEFAULT 0;

-- Update any existing points to recalculate with new system
-- Note: This would require knowing the original A Grade count for each event
-- For now, we'll just convert existing INT values to DECIMAL format

SELECT 'Points column updated to DECIMAL(15,10) successfully!' as status;
