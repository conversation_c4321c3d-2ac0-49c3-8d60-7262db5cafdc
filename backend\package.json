{"name": "nraa-rankings-backend", "version": "1.0.0", "description": "Backend API for NRAA Rankings System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-database.js"}, "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["nraa", "rankings", "shooting", "competition", "api"], "author": "NRAA Rankings System", "license": "MIT"}