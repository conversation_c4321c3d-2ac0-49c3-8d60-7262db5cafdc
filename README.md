# NRAA EMMS Grading - Shooter Rankings System

A web application for managing and displaying shooter rankings using the Kings Prize system with a 3-year rolling average.

## Features

- **Rankings Display**: View shooter rankings in a sortable table with search functionality
- **Score Entry**: Complete interface for entering event results and shooter positions
- **Auto-Increment Positions**: Smart position incrementing from 1 to A Grade count
- **NRAA Auto-Scraper**: Automatically import results from the official NRAA website
- **Event Management**: View event history and manage past competitions
- **3-Year Rolling Average**: Automatic calculation using only recent events
- **Kings Prize Scoring System**: Accurate point calculation based on position and A Grade count
- **Data Validation**: Comprehensive validation to prevent duplicate entries and errors
- **Data Import/Export**: Multiple import methods including web scraping and file import
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Docker Development Environment

### Prerequisites

- Docker installed on your system
- Docker Compose installed

### Quick Start

1. **Build and run the application:**
   ```bash
   docker-compose up --build
   ```

2. **Access the application:**
   - Open your browser and go to: http://localhost:8080

3. **For development with hot reload:**
   ```bash
   docker-compose --profile dev up
   ```
   - Access at: http://localhost:3000

### Docker Commands

- **Start the application:**
  ```bash
  docker-compose up
  ```

- **Start in background:**
  ```bash
  docker-compose up -d
  ```

- **Stop the application:**
  ```bash
  docker-compose down
  ```

- **Rebuild after changes:**
  ```bash
  docker-compose up --build
  ```

- **View logs:**
  ```bash
  docker-compose logs
  ```

### Development

The application consists of:
- `index.html` - Main HTML structure
- `styles.css` - Styling and layout
- `ranking-system.js` - Core ranking logic and UI interactions
- `data-manager.js` - Sample data and data management functions

### File Structure

```
.
├── index.html          # Main HTML file
├── styles.css          # CSS styles
├── ranking-system.js   # Main JavaScript logic
├── data-manager.js     # Data management utilities
├── Dockerfile          # Docker configuration
├── docker-compose.yml  # Docker Compose configuration
└── README.md          # This file
```

### Using the Application

The application starts with a clean slate - no sample data is loaded by default.

#### Getting Started
1. **Open the application** at http://localhost:8080
2. **Navigate to the Score Entry tab** to add your first event
3. **Start building your rankings** by entering competition results

#### Score Entry
1. **Navigate to Score Entry tab**
2. **Enter Event Information:**
   - Select event date
   - Choose location (NSW, VIC, QLD, SA, WA, TAS, NT, ACT)
   - Enter A Grade count (number of A Grade shooters)

3. **Add Shooter Results:**
   - Enter shooter name (autocomplete available after first event)
   - Position auto-increments from 1 to A Grade count (toggle with 🔢 Auto button)
   - Click "Add Shooter" or press Enter to add each shooter
   - Progress bar shows how many A Grade shooters have been added
   - Repeat for all shooters

4. **Save Event:**
   - Review all entries in the results list
   - Click "Save Event" to add to rankings
   - Data is automatically validated

#### Rankings View
- View current rankings based on 3-year rolling average
- Search for specific shooters
- See total points, event count, and best results

#### Events History
- View all recorded events
- See event details including top results
- Review historical data

#### Auto-Increment Position Feature
- **Enabled by default**: Positions automatically increment from 1 to A Grade count
- **Toggle control**: Click the "🔢 Auto" button to enable/disable auto-increment
- **Manual mode**: Switch to "✋ Manual" for custom position entry
- **Progress tracking**: Visual progress bar shows completion status
- **Smart validation**: Warns when exceeding A Grade count limit

#### NRAA Auto-Scraper
- **Automatic Import**: Scrape results directly from results.nraa.com.au
- **Multi-Year Support**: Import from 2023, 2024, and 2025 results
- **State-by-State**: Automatically finds and imports all state competitions
- **Kings Aggregate Only**: Filters out Grand Aggregate and other non-Kings results
- **Duplicate Detection**: Prevents importing the same event twice
- **Progress Tracking**: Real-time progress display during import
- **Error Handling**: Robust error handling with detailed feedback

### Data Management

#### Programmatic Data Entry
Use the functions in `data-manager.js` for bulk operations:

```javascript
// Quick add an event
quickAddEvent('NSW', 45, [
    { shooter: 'John Smith', position: 1 },
    { shooter: 'Jane Doe', position: 2 }
]);

// Export data for backup
exportData();

// View statistics
showStatistics();
```

#### Data Export/Import
- Use `exportData()` to download a JSON backup
- Use `importData(jsonData)` to restore from backup
- Use `clearAllData()` to reset the system (with confirmation)

#### NRAA Auto-Scraper Usage

**⚠️ CORS Limitation**: Due to browser security restrictions, automatic scraping may be blocked. The system provides several alternatives:

**🚀 Automatic Import** (if CORS allows):
1. **Navigate to Data Import tab**
2. **Full Import**: Click "🚀 Import All Years" to scrape 2023-2025 results
3. **Specific URL**: Enter a specific NRAA results URL and click "Import URL"
4. **Test Import**: Use "🧪 Test Import" to try with sample data first

**📋 Manual Import Alternatives**:
1. **Discover URLs**: Find actual competition URLs from year pages (no assumptions)
2. **Show All URLs**: Get direct links to all NRAA competition pages
3. **Paste Data**: Copy-paste table data directly from NRAA website
4. **File Import**: Save NRAA pages as HTML and import them
5. **JSON Import**: Import from exported backup files

**Supported URL Formats:**
- Year pages: `https://www.results.nraa.com.au/all-kings-prize-results-2024/`
- State competitions: `https://www.results.nraa.com.au/nqra-kings-2024-results/`

**Console Commands:**
```javascript
// Import all available years
startFullScraping()

// Test with sample data
testScrapingFunction()

// Import specific URL
document.getElementById('specific-url').value = 'URL_HERE'
scrapeSpecificUrlFunction()

// Quick imports
quickImportYear(2024)
quickImportState('NSW', 2024)
```

#### Manual Import Process (CORS Workaround)

**📋 Copy-Paste Method**:
1. Click "🔗 Show All URLs" to get NRAA competition links
2. Visit the NRAA results page in a new tab
3. Find collapsible sections like "Match 21 - Aggregate - Kings"
4. **Option A - Table Copy**: Expand the Kings section and copy just the table
5. **Option B - HTML Copy**: Copy entire page source (Ctrl+U → Ctrl+A → Ctrl+C)
6. Click "📋 Paste Data" in the Data Import tab
7. Paste the data and set event details
8. Click "Import Data"

**🔍 URL Discovery Method**:
1. Click "🔍 Discover URLs" and select a year (2023, 2024, 2025)
2. Visit the year results page (link provided)
3. View page source (Ctrl+U) and copy all HTML
4. Paste HTML into the discovery tool
5. System finds all actual competition URLs (no pattern assumptions)
6. Import individual competitions or all at once

**💡 Tips for Manual Import**:
- **Collapsible Sections**: Results are in expandable sections like "Match 21 - Aggregate - Kings"
- **Kings Only**: Look for "Kings Aggregate" sections (ignore "Grand Aggregate" and daily matches)
- **Two Methods**: Copy just the table OR copy entire HTML source (both work)
- **Headers Important**: Always include table headers for best parsing
- **Multiple Imports**: Import one competition at a time for better control
- **HTML Parsing**: System automatically finds Kings sections in HTML source

**🌐 CORS Solutions for Auto-Scraping**:
- **Server Environment**: Run on a web server (no CORS restrictions)
- **Browser Extension**: Temporarily disable CORS for results.nraa.com.au
- **Development Mode**: Use `--disable-web-security` flag in Chrome (development only)

#### Loading Sample Data (Optional)
If you want to test with sample data, you can:
1. Edit `data-manager.js` and uncomment the sample data in `loadSampleData()`
2. Refresh the page to load the sample events
3. Or use the browser console: `loadSampleData()` (after uncommenting the code)

### Scoring System

The Kings Prize scoring system awards points based on:
- **1st place**: Full A Grade count
- **2nd place**: 80% of A Grade count
- **3rd place**: 60% of A Grade count
- **4th-5th place**: 40% of A Grade count
- **6th-10th place**: 20% of A Grade count
- **Below 10th**: 0 points

Only events from the last 3 years are included in the rankings.

### Data Validation

The system includes comprehensive validation:
- **Event Requirements**: Date, location, and A Grade count are mandatory
- **Shooter Requirements**: Name and position are required for each result
- **No Duplicates**: Prevents duplicate positions or shooters in the same event
- **Position Validation**: Positions must be positive integers
- **Name Validation**: Shooter names cannot be empty

### Browser Console Commands

Open browser developer tools and use these commands:
```javascript
// View current statistics
showStatistics();

// Export all data
exportData();

// Clear all data (with confirmation)
clearAllData();

// Get all shooter names
rankingSystem.getAllShooterNames();

// Get current rankings
rankingSystem.getRankings();
```
