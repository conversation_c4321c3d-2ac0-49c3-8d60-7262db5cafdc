const express = require('express');
const { executeQuery } = require('../config/database');

const router = express.Router();

// GET /api/rankings - Get current rankings
router.get('/', async (req, res) => {
  try {
    const { limit = 100, offset = 0, search } = req.query;
    
    let query = `
      SELECT 
        shooter_id,
        shooter_name,
        total_events,
        total_points,
        best_position,
        last_event_date,
        avg_points_per_event,
        ROW_NUMBER() OVER (ORDER BY total_points DESC, total_events DESC, best_position ASC) as rank_position
      FROM shooter_rankings
    `;
    
    const params = [];
    
    if (search) {
      query += ' WHERE shooter_name LIKE ?';
      params.push(`%${search}%`);
    }
    
    query += ' ORDER BY total_points DESC, total_events DESC, best_position ASC';
    query += ' LIMIT ? OFFSET ?';
    
    params.push(parseInt(limit), parseInt(offset));
    
    const rankings = await executeQuery(query, params);
    
    // Get total count for pagination
    let countQuery = 'SELECT COUNT(*) as total FROM shooter_rankings';
    const countParams = [];
    
    if (search) {
      countQuery += ' WHERE shooter_name LIKE ?';
      countParams.push(`%${search}%`);
    }
    
    const countResult = await executeQuery(countQuery, countParams);
    const total = countResult[0].total;
    
    res.json({
      rankings,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total
      }
    });
  } catch (error) {
    console.error('Error fetching rankings:', error);
    res.status(500).json({ error: 'Failed to fetch rankings' });
  }
});

// GET /api/rankings/shooter/:id - Get specific shooter's ranking details
router.get('/shooter/:id', async (req, res) => {
  try {
    const shooterId = parseInt(req.params.id);
    
    // Get shooter ranking
    const rankings = await executeQuery(`
      SELECT 
        shooter_id,
        shooter_name,
        total_events,
        total_points,
        best_position,
        last_event_date,
        avg_points_per_event
      FROM shooter_rankings
      WHERE shooter_id = ?
    `, [shooterId]);
    
    if (rankings.length === 0) {
      return res.status(404).json({ error: 'Shooter not found in rankings' });
    }
    
    const shooter = rankings[0];
    
    // Get shooter's event history
    const eventHistory = await executeQuery(`
      SELECT 
        e.id as event_id,
        e.date,
        e.location,
        e.competition_name,
        r.position,
        r.score,
        r.points,
        e.a_grade_count
      FROM results r
      JOIN events e ON r.event_id = e.id
      WHERE r.shooter_id = ?
        AND e.date >= DATE_SUB(CURDATE(), INTERVAL 3 YEAR)
      ORDER BY e.date DESC
    `, [shooterId]);
    
    // Calculate rank position
    const rankQuery = await executeQuery(`
      SELECT COUNT(*) + 1 as rank_position
      FROM shooter_rankings
      WHERE total_points > ? 
        OR (total_points = ? AND total_events > ?)
        OR (total_points = ? AND total_events = ? AND best_position < ?)
    `, [
      shooter.total_points,
      shooter.total_points, shooter.total_events,
      shooter.total_points, shooter.total_events, shooter.best_position
    ]);
    
    shooter.rank_position = rankQuery[0].rank_position;
    shooter.event_history = eventHistory;
    
    res.json(shooter);
  } catch (error) {
    console.error('Error fetching shooter ranking:', error);
    res.status(500).json({ error: 'Failed to fetch shooter ranking' });
  }
});

// GET /api/rankings/stats - Get ranking statistics
router.get('/stats', async (req, res) => {
  try {
    // Get overall statistics
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total_shooters,
        SUM(total_events) as total_event_participations,
        AVG(total_points) as avg_points,
        MAX(total_points) as max_points,
        MIN(total_points) as min_points
      FROM shooter_rankings
    `);
    
    // Get events statistics
    const eventStats = await executeQuery(`
      SELECT 
        COUNT(*) as total_events,
        COUNT(DISTINCT location) as total_locations,
        AVG(a_grade_count) as avg_a_grade_count,
        MIN(date) as earliest_event,
        MAX(date) as latest_event
      FROM events
      WHERE date >= DATE_SUB(CURDATE(), INTERVAL 3 YEAR)
    `);
    
    // Get top performers
    const topPerformers = await executeQuery(`
      SELECT 
        shooter_name,
        total_points,
        total_events,
        best_position
      FROM shooter_rankings
      ORDER BY total_points DESC
      LIMIT 10
    `);
    
    // Get location statistics
    const locationStats = await executeQuery(`
      SELECT 
        location,
        COUNT(*) as event_count,
        AVG(a_grade_count) as avg_participants
      FROM events
      WHERE date >= DATE_SUB(CURDATE(), INTERVAL 3 YEAR)
      GROUP BY location
      ORDER BY event_count DESC
    `);
    
    res.json({
      overall: stats[0],
      events: eventStats[0],
      topPerformers,
      locationStats
    });
  } catch (error) {
    console.error('Error fetching ranking statistics:', error);
    res.status(500).json({ error: 'Failed to fetch ranking statistics' });
  }
});

// GET /api/rankings/history/:shooterId - Get shooter's ranking history over time
router.get('/history/:shooterId', async (req, res) => {
  try {
    const shooterId = parseInt(req.params.shooterId);
    
    // Get shooter's cumulative points over time
    const history = await executeQuery(`
      SELECT 
        e.date,
        e.location,
        r.position,
        r.points,
        SUM(r.points) OVER (ORDER BY e.date) as cumulative_points
      FROM results r
      JOIN events e ON r.event_id = e.id
      WHERE r.shooter_id = ?
        AND e.date >= DATE_SUB(CURDATE(), INTERVAL 3 YEAR)
      ORDER BY e.date ASC
    `, [shooterId]);
    
    res.json(history);
  } catch (error) {
    console.error('Error fetching shooter history:', error);
    res.status(500).json({ error: 'Failed to fetch shooter history' });
  }
});

module.exports = router;
