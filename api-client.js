// API Client for NRAA Rankings Backend
// Handles all communication with the Node.js/MySQL backend

class APIClient {
    constructor() {
        this.baseURL = window.location.hostname === 'localhost' 
            ? 'http://localhost:3001/api'
            : '/api'; // Use relative path in production
        this.isOnline = true;
        this.retryAttempts = 3;
        this.retryDelay = 1000;
    }

    // Generic request method with error handling and retries
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
            try {
                const response = await fetch(url, defaultOptions);
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                this.isOnline = true;
                return data;
                
            } catch (error) {
                console.error(`API request failed (attempt ${attempt}/${this.retryAttempts}):`, error);
                
                if (attempt === this.retryAttempts) {
                    this.isOnline = false;
                    throw error;
                }
                
                // Wait before retrying
                await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
            }
        }
    }

    // Health check
    async healthCheck() {
        try {
            const response = await this.request('/health');
            this.isOnline = true;
            return response;
        } catch (error) {
            this.isOnline = false;
            throw error;
        }
    }

    // Events API
    async getEvents(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return await this.request(`/events${queryString ? '?' + queryString : ''}`);
    }

    async getEvent(id) {
        return await this.request(`/events/${id}`);
    }

    async createEvent(eventData) {
        return await this.request('/events', {
            method: 'POST',
            body: JSON.stringify(eventData)
        });
    }

    async updateEvent(id, eventData) {
        return await this.request(`/events/${id}`, {
            method: 'PUT',
            body: JSON.stringify(eventData)
        });
    }

    async deleteEvent(id) {
        return await this.request(`/events/${id}`, {
            method: 'DELETE'
        });
    }

    // Rankings API
    async getRankings(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return await this.request(`/rankings${queryString ? '?' + queryString : ''}`);
    }

    async getShooterRanking(shooterId) {
        return await this.request(`/rankings/shooter/${shooterId}`);
    }

    async getRankingStats() {
        return await this.request('/rankings/stats');
    }

    async getShooterHistory(shooterId) {
        return await this.request(`/rankings/history/${shooterId}`);
    }

    // Shooters API
    async getShooters(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return await this.request(`/shooters${queryString ? '?' + queryString : ''}`);
    }

    async getShooterAutocomplete(query) {
        return await this.request(`/shooters/autocomplete?q=${encodeURIComponent(query)}`);
    }

    async getShooter(id) {
        return await this.request(`/shooters/${id}`);
    }

    // Import API
    async bulkImport(eventsData) {
        return await this.request('/import/bulk', {
            method: 'POST',
            body: JSON.stringify({ events: eventsData })
        });
    }

    async validateImport(eventsData) {
        return await this.request('/import/validate', {
            method: 'POST',
            body: JSON.stringify({ events: eventsData })
        });
    }

    async getImportStats() {
        return await this.request('/import/stats');
    }

    // Utility methods
    isBackendAvailable() {
        return this.isOnline;
    }

    async testConnection() {
        try {
            await this.healthCheck();
            return true;
        } catch (error) {
            return false;
        }
    }

    // Convert frontend event format to backend format
    formatEventForAPI(event) {
        return {
            date: event.date instanceof Date ? event.date.toISOString().split('T')[0] : event.date,
            location: event.location,
            aGradeCount: event.aGradeCount,
            competitionName: event.competition || `${event.location} Kings ${new Date(event.date).getFullYear()}`,
            sourceUrl: event.sourceUrl || null,
            results: event.results.map(result => ({
                shooter: result.shooter,
                position: result.position,
                score: result.score || null
            }))
        };
    }

    // Convert backend event format to frontend format
    formatEventFromAPI(apiEvent) {
        return {
            id: apiEvent.id,
            date: new Date(apiEvent.date),
            location: apiEvent.location,
            aGradeCount: apiEvent.a_grade_count,
            competition: apiEvent.competition_name,
            sourceUrl: apiEvent.source_url,
            results: apiEvent.results ? apiEvent.results.map(result => ({
                shooter: result.shooter_name,
                position: result.position,
                score: result.score,
                points: result.points
            })) : []
        };
    }

    // Convert backend ranking format to frontend format
    formatRankingFromAPI(apiRanking) {
        return {
            shooterId: apiRanking.shooter_id,
            shooter: apiRanking.shooter_name,
            totalEvents: apiRanking.total_events,
            totalPoints: apiRanking.total_points,
            bestPosition: apiRanking.best_position,
            lastEventDate: new Date(apiRanking.last_event_date),
            avgPointsPerEvent: parseFloat(apiRanking.avg_points_per_event || 0),
            rank: apiRanking.rank_position
        };
    }

    // Error handling helper
    handleAPIError(error, context = '') {
        console.error(`API Error ${context}:`, error);
        
        if (!this.isOnline) {
            return 'Backend server is not available. Please check your connection.';
        }
        
        if (error.message.includes('fetch')) {
            return 'Network error. Please check your internet connection.';
        }
        
        if (error.message.includes('409')) {
            return 'This data already exists in the database.';
        }
        
        if (error.message.includes('400')) {
            return 'Invalid data format. Please check your input.';
        }
        
        return error.message || 'An unexpected error occurred.';
    }
}

// Global API client instance
const apiClient = new APIClient();

// Test backend connection on page load
document.addEventListener('DOMContentLoaded', async () => {
    try {
        const isAvailable = await apiClient.testConnection();
        if (isAvailable) {
            console.log('✅ Backend API connected successfully');
            
            // Show backend status in UI
            const statusElement = document.getElementById('backend-status');
            if (statusElement) {
                statusElement.textContent = '🟢 Backend Connected';
                statusElement.className = 'backend-status online';
            }
        } else {
            console.warn('⚠️ Backend API not available - using local storage mode');
            
            const statusElement = document.getElementById('backend-status');
            if (statusElement) {
                statusElement.textContent = '🔴 Backend Offline';
                statusElement.className = 'backend-status offline';
            }
        }
    } catch (error) {
        console.error('Backend connection test failed:', error);
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = APIClient;
}
