<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Score Entry Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 15px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>NRAA Score Entry System - Test Page</h1>
    
    <div class="test-section">
        <h2>Test 1: Basic Score Entry Validation</h2>
        <button onclick="testBasicValidation()">Run Test</button>
        <div id="test1-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Duplicate Prevention</h2>
        <button onclick="testDuplicatePrevention()">Run Test</button>
        <div id="test2-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Points Calculation</h2>
        <button onclick="testPointsCalculation()">Run Test</button>
        <div id="test3-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 4: Add Sample Event</h2>
        <button onclick="addSampleEvent()">Add Test Event</button>
        <div id="test4-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Current System State</h2>
        <button onclick="showSystemState()">Show State</button>
        <div id="system-state"></div>
    </div>

    <script src="ranking-system.js"></script>
    <script src="data-manager.js"></script>
    <script>
        function testBasicValidation() {
            const result = document.getElementById('test1-result');
            
            // Test empty event data
            const emptyEvent = { date: '', location: '', aGradeCount: 0, results: [] };
            const errors = rankingSystem.validateEventData(emptyEvent);
            
            if (errors.length > 0) {
                result.innerHTML = `<div class="success">✓ Validation working correctly. Found ${errors.length} errors:<br><pre>${errors.join('\n')}</pre></div>`;
            } else {
                result.innerHTML = `<div class="error">✗ Validation failed - should have found errors</div>`;
            }
        }
        
        function testDuplicatePrevention() {
            const result = document.getElementById('test2-result');
            
            // Test duplicate positions
            const duplicatePositions = {
                date: '2024-01-01',
                location: 'NSW',
                aGradeCount: 50,
                results: [
                    { shooter: 'John Smith', position: 1 },
                    { shooter: 'Jane Doe', position: 1 }  // Duplicate position
                ]
            };
            
            const errors = rankingSystem.validateEventData(duplicatePositions);
            const hasDuplicateError = errors.some(error => error.includes('Duplicate positions'));
            
            if (hasDuplicateError) {
                result.innerHTML = `<div class="success">✓ Duplicate position detection working</div>`;
            } else {
                result.innerHTML = `<div class="error">✗ Duplicate position detection failed</div>`;
            }
        }
        
        function testPointsCalculation() {
            const result = document.getElementById('test3-result');
            
            const aGradeCount = 50;
            const tests = [
                { position: 1, expected: 50 },
                { position: 2, expected: 40 },
                { position: 3, expected: 30 },
                { position: 5, expected: 20 },
                { position: 10, expected: 10 },
                { position: 15, expected: 0 }
            ];
            
            let allPassed = true;
            let resultText = '<div class="success">Points calculation tests:<br>';
            
            tests.forEach(test => {
                const calculated = rankingSystem.calculatePoints(test.position, aGradeCount);
                const passed = calculated === test.expected;
                allPassed = allPassed && passed;
                
                resultText += `Position ${test.position}: ${calculated} points (expected ${test.expected}) ${passed ? '✓' : '✗'}<br>`;
            });
            
            resultText += '</div>';
            
            if (!allPassed) {
                resultText = resultText.replace('success', 'error');
            }
            
            result.innerHTML = resultText;
        }
        
        function addSampleEvent() {
            const result = document.getElementById('test4-result');
            
            const testEvent = {
                date: new Date().toISOString().split('T')[0],
                location: 'TEST',
                aGradeCount: 30,
                results: [
                    { shooter: 'Test Shooter 1', position: 1 },
                    { shooter: 'Test Shooter 2', position: 2 },
                    { shooter: 'Test Shooter 3', position: 3 }
                ]
            };
            
            try {
                rankingSystem.addEvent(testEvent);
                result.innerHTML = `<div class="success">✓ Test event added successfully</div>`;
            } catch (error) {
                result.innerHTML = `<div class="error">✗ Failed to add test event: ${error.message}</div>`;
            }
        }
        
        function showSystemState() {
            const result = document.getElementById('system-state');
            const stats = getStatistics();
            
            result.innerHTML = `
                <div class="success">
                    <h3>Current System Statistics:</h3>
                    <pre>${JSON.stringify(stats, null, 2)}</pre>
                    
                    <h3>All Shooter Names:</h3>
                    <pre>${JSON.stringify(rankingSystem.getAllShooterNames(), null, 2)}</pre>
                    
                    <h3>Top 5 Rankings:</h3>
                    <pre>${JSON.stringify(rankingSystem.getRankings().slice(0, 5), null, 2)}</pre>
                </div>
            `;
        }
        
        // Auto-run basic tests on page load
        window.addEventListener('load', function() {
            console.log('Score Entry Test Page Loaded');
            console.log('Available functions: testBasicValidation(), testDuplicatePrevention(), testPointsCalculation(), addSampleEvent(), showSystemState()');
        });
    </script>
</body>
</html>
