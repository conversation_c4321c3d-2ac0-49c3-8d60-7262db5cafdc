<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NRAA Points Calculation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .result { margin: 5px 0; }
        .pass { color: green; }
        .fail { color: red; }
        .expected { color: #666; font-style: italic; }
    </style>
</head>
<body>
    <h1>NRAA Kings Prize Points Calculation Test</h1>
    <p><strong>New Formula:</strong> points = aGradeCount / (2^(position-1))</p>
    <p><strong>Precision:</strong> 10 decimal places</p>
    
    <div id="test-results"></div>
    
    <script>
        // Calculate points based on position and A Grade count
        // New system: 1st gets full count, each subsequent position gets half the points of previous
        // Formula: points = aGradeCount / (2^(position-1))
        // Calculated to 10 decimal places
        function calculatePoints(position, aGradeCount) {
            if (position < 1 || aGradeCount < 1) return 0;
            
            const points = aGradeCount / Math.pow(2, position - 1);
            
            // Round to 10 decimal places
            return Math.round(points * 10000000000) / 10000000000;
        }
        
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            let html = '';
            
            // Test case 1: 45 shooters (as mentioned in the example)
            html += '<div class="test-section">';
            html += '<h3>Test Case 1: 45 shooters</h3>';
            html += testPosition(1, 45, 45);
            html += testPosition(2, 45, 22.5);
            html += testPosition(3, 45, 11.25);
            html += testPosition(4, 45, 5.625);
            html += testPosition(5, 45, 2.8125);
            html += testPosition(10, 45, 0.087890625);
            html += '</div>';
            
            // Test case 2: 30 shooters
            html += '<div class="test-section">';
            html += '<h3>Test Case 2: 30 shooters</h3>';
            html += testPosition(1, 30, 30);
            html += testPosition(2, 30, 15);
            html += testPosition(3, 30, 7.5);
            html += testPosition(4, 30, 3.75);
            html += '</div>';
            
            // Test case 3: 10 shooters
            html += '<div class="test-section">';
            html += '<h3>Test Case 3: 10 shooters</h3>';
            html += testPosition(1, 10, 10);
            html += testPosition(2, 10, 5);
            html += testPosition(3, 10, 2.5);
            html += testPosition(10, 10, 0.009765625);
            html += '</div>';
            
            // Edge cases
            html += '<div class="test-section">';
            html += '<h3>Edge Cases</h3>';
            html += testPosition(0, 45, 0, 'Invalid position (0)');
            html += testPosition(1, 0, 0, 'Invalid aGradeCount (0)');
            html += testPosition(-1, 45, 0, 'Negative position');
            html += testPosition(1, -45, 0, 'Negative aGradeCount');
            html += '</div>';
            
            resultsDiv.innerHTML = html;
        }
        
        function testPosition(position, aGradeCount, expected, description = null) {
            const actual = calculatePoints(position, aGradeCount);
            const passed = Math.abs(actual - expected) < 0.0000000001; // Allow for floating point precision
            const desc = description || `Position ${position}`;
            
            return `<div class="result ${passed ? 'pass' : 'fail'}">
                ${desc}: ${actual} points 
                <span class="expected">(expected: ${expected})</span>
                ${passed ? '✓' : '✗'}
            </div>`;
        }
        
        // Run tests when page loads
        window.onload = runTests;
    </script>
</body>
</html>
