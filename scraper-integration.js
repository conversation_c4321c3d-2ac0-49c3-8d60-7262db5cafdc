// NRAA Scraper Integration with Ranking System
// Handles the integration between the scraper and our ranking system

class ScraperIntegration {
    constructor() {
        this.scraper = new NRAAScraper();
        this.isScrapingInProgress = false;
        this.scrapingProgress = {
            total: 0,
            completed: 0,
            current: '',
            errors: []
        };
    }

    // Main scraping function with progress tracking
    async scrapeAndImportResults() {
        if (this.isScrapingInProgress) {
            alert('Scraping is already in progress. Please wait...');
            return;
        }

        this.isScrapingInProgress = true;
        this.resetProgress();
        
        try {
            // Show progress modal
            this.showProgressModal();
            
            // Start scraping
            this.updateProgress('Initializing scraper...', 0, 0);
            
            const scrapedResults = await this.scraper.scrapeAllYears();
            
            if (scrapedResults.length === 0) {
                throw new Error('No results were scraped. Please check the website structure or network connection.');
            }
            
            this.updateProgress('Converting results to event format...', 90, 100);
            
            // Convert to our event format
            const events = this.scraper.convertToEventFormat(scrapedResults);
            
            this.updateProgress('Importing events into ranking system...', 95, 100);
            
            // Import into ranking system
            const importResults = await this.importEvents(events);
            
            this.updateProgress('Import completed!', 100, 100);
            
            // Show success message
            this.showImportResults(importResults);
            
        } catch (error) {
            console.error('Scraping failed:', error);

            // Handle CORS errors specifically
            if (error.message.includes('CORS') || error.message.includes('fetch')) {
                this.showCORSError();
            } else {
                this.showError('Scraping failed: ' + error.message);
            }
        } finally {
            this.isScrapingInProgress = false;
            this.hideProgressModal();
        }
    }

    // Import events into the ranking system
    async importEvents(events) {
        const results = {
            imported: 0,
            skipped: 0,
            errors: 0,
            details: []
        };

        for (const eventData of events) {
            try {
                // Check if event already exists (basic duplicate detection)
                const existingEvent = this.findExistingEvent(eventData);
                
                if (existingEvent) {
                    results.skipped++;
                    results.details.push(`Skipped: ${eventData.competition} (already exists)`);
                    continue;
                }

                // Validate event data
                const errors = rankingSystem.validateEventData(eventData);
                if (errors.length > 0) {
                    results.errors++;
                    results.details.push(`Error in ${eventData.competition}: ${errors.join(', ')}`);
                    continue;
                }

                // Add to ranking system
                rankingSystem.addEvent(eventData);
                results.imported++;
                results.details.push(`Imported: ${eventData.competition} (${eventData.results.length} shooters)`);
                
            } catch (error) {
                results.errors++;
                results.details.push(`Error importing ${eventData.competition}: ${error.message}`);
            }
        }

        // Update displays
        displayRankings();
        displayEvents();

        return results;
    }

    // Basic duplicate detection
    findExistingEvent(newEvent) {
        return rankingSystem.events.find(existing => {
            return existing.location === newEvent.location &&
                   existing.date.getFullYear() === parseInt(newEvent.date.split('-')[0]) &&
                   existing.results.length === newEvent.results.length;
        });
    }

    // Progress tracking methods
    resetProgress() {
        this.scrapingProgress = {
            total: 0,
            completed: 0,
            current: '',
            errors: []
        };
    }

    updateProgress(message, completed, total) {
        this.scrapingProgress.current = message;
        this.scrapingProgress.completed = completed;
        this.scrapingProgress.total = total;
        
        console.log(`Progress: ${message} (${completed}/${total})`);
        
        // Update progress modal if it exists
        this.updateProgressModal();
    }

    // UI Methods for progress display
    showProgressModal() {
        const modal = document.createElement('div');
        modal.id = 'scraping-progress-modal';
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <h3>Scraping NRAA Results</h3>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="scraping-progress-fill"></div>
                    </div>
                    <div class="progress-text" id="scraping-progress-text">Initializing...</div>
                </div>
                <div class="progress-details" id="scraping-progress-details"></div>
                <button id="cancel-scraping-btn" onclick="scraperIntegration.cancelScraping()">Cancel</button>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    updateProgressModal() {
        const progressFill = document.getElementById('scraping-progress-fill');
        const progressText = document.getElementById('scraping-progress-text');
        
        if (progressFill && progressText) {
            const percentage = this.scrapingProgress.total > 0 ? 
                (this.scrapingProgress.completed / this.scrapingProgress.total) * 100 : 0;
            
            progressFill.style.width = `${percentage}%`;
            progressText.textContent = this.scrapingProgress.current;
        }
    }

    hideProgressModal() {
        const modal = document.getElementById('scraping-progress-modal');
        if (modal) {
            modal.remove();
        }
    }

    showImportResults(results) {
        const message = `
Import completed!

✅ Imported: ${results.imported} events
⏭️ Skipped: ${results.skipped} events (duplicates)
❌ Errors: ${results.errors} events

${results.details.slice(0, 10).join('\n')}
${results.details.length > 10 ? `\n... and ${results.details.length - 10} more` : ''}
        `;
        
        alert(message);
    }

    showError(message) {
        alert('❌ ' + message);
    }

    showCORSError() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content" style="max-width: 600px;">
                <h3>🚫 Auto-Scraping Blocked</h3>
                <p>Browser security restrictions (CORS) prevent automatic scraping of the NRAA website.</p>

                <h4>📋 Alternative Options:</h4>
                <div style="margin: 15px 0;">
                    <button onclick="showManualImportInstructions(); this.parentElement.parentElement.parentElement.remove();"
                            style="width: 100%; padding: 12px; margin-bottom: 10px; background-color: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        📋 Manual Import Instructions
                    </button>

                    <button onclick="showAllNRAAUrls(); this.parentElement.parentElement.parentElement.remove();"
                            style="width: 100%; padding: 12px; margin-bottom: 10px; background-color: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        🔗 Show All NRAA URLs
                    </button>

                    <button onclick="showPasteImport(); this.parentElement.parentElement.parentElement.remove();"
                            style="width: 100%; padding: 12px; margin-bottom: 10px; background-color: #ffc107; color: #212529; border: none; border-radius: 4px; cursor: pointer;">
                        📋 Paste Table Data
                    </button>

                    <button onclick="showAllYearDiscovery(); this.parentElement.parentElement.parentElement.remove();"
                            style="width: 100%; padding: 12px; margin-bottom: 10px; background-color: #9c27b0; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        🔍 Discover Real URLs
                    </button>
                </div>

                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin: 15px 0; font-size: 14px;">
                    <strong>💡 Pro Tip:</strong> For automatic scraping, run this application on a web server or use a CORS-disabling browser extension temporarily.
                </div>

                <button onclick="this.parentElement.parentElement.remove()"
                        style="width: 100%; padding: 10px; background-color: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Close
                </button>
            </div>
        `;

        document.body.appendChild(modal);
    }

    cancelScraping() {
        // Note: This is a simple implementation. 
        // For a more robust solution, you'd need to implement proper cancellation
        this.isScrapingInProgress = false;
        this.hideProgressModal();
        alert('Scraping cancelled');
    }

    // Test scraping with a single state/year
    async testScraping(year = '2024', stateCode = 'NQRA') {
        try {
            console.log(`Testing scraping for ${stateCode} ${year}...`);
            
            // Construct test URL
            const testUrl = `${this.scraper.baseUrl}/all-kings-prize-results-${year}/`;
            
            // This would need to be implemented based on the actual site structure
            console.log(`Would scrape: ${testUrl}`);
            
            // For now, return mock data for testing
            return this.getMockTestData(year, stateCode);
            
        } catch (error) {
            console.error('Test scraping failed:', error);
            throw error;
        }
    }

    // Mock data for testing the integration
    getMockTestData(year, stateCode) {
        const state = this.scraper.stateMapping[stateCode] || stateCode;
        
        return [{
            date: `${year}-03-15`,
            location: state,
            aGradeCount: 25,
            results: [
                { shooter: 'Test Shooter 1', position: 1 },
                { shooter: 'Test Shooter 2', position: 2 },
                { shooter: 'Test Shooter 3', position: 3 },
                { shooter: 'Test Shooter 4', position: 4 },
                { shooter: 'Test Shooter 5', position: 5 }
            ],
            competition: `${state} Kings ${year} (Test Data)`,
            sourceUrl: `test-url-${stateCode}-${year}`
        }];
    }

    // Manual URL scraping for testing specific pages
    async scrapeSpecificUrl(url) {
        try {
            console.log(`Scraping specific URL: ${url}`);
            
            // Extract state and year from URL if possible
            const urlMatch = url.match(/([a-z]+)-kings-(\d{4})-results/i);
            const stateCode = urlMatch ? urlMatch[1].toUpperCase() : 'UNKNOWN';
            const year = urlMatch ? urlMatch[2] : new Date().getFullYear().toString();
            
            const stateLink = {
                state: this.scraper.stateMapping[stateCode] || stateCode,
                stateCode: stateCode,
                year: year,
                url: url,
                text: `${stateCode} ${year}`
            };
            
            const results = await this.scraper.scrapeStateCompetition(stateLink);
            
            if (results.length > 0) {
                const events = this.scraper.convertToEventFormat(results);
                return events[0]; // Return the first (and likely only) event
            } else {
                throw new Error('No results found at the specified URL');
            }
            
        } catch (error) {
            console.error('URL scraping failed:', error);
            throw error;
        }
    }
}

// Global instance
const scraperIntegration = new ScraperIntegration();

// Console helper functions
function testScraping(year = '2024', stateCode = 'NQRA') {
    return scraperIntegration.testScraping(year, stateCode);
}

function scrapeSpecificUrl(url) {
    return scraperIntegration.scrapeSpecificUrl(url);
}

function startFullScraping() {
    return scraperIntegration.scrapeAndImportResults();
}
