-- NRAA Rankings Database Initialization Script
-- This script sets up the initial database structure

-- Ensure we're using the correct database
USE nraa_rankings;

-- Set charset and collation
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- Create events table
CREATE TABLE IF NOT EXISTS events (
  id INT AUTO_INCREMENT PRIMARY KEY,
  date DATE NOT NULL,
  location VARCHAR(50) NOT NULL,
  a_grade_count INT NOT NULL,
  competition_name VARCHAR(255),
  source_url TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_date (date),
  INDEX idx_location (location),
  INDEX idx_location_date (location, date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create shooters table
CREATE TABLE IF NOT EXISTS shooters (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VA<PERSON>HAR(255) NOT NULL,
  normalized_name VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_normalized_name (normalized_name),
  INDEX idx_name (name),
  INDEX idx_normalized_name (normalized_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create results table
CREATE TABLE IF NOT EXISTS results (
  id INT AUTO_INCREMENT PRIMARY KEY,
  event_id INT NOT NULL,
  shooter_id INT NOT NULL,
  position INT NOT NULL,
  score DECIMAL(10,2),
  points DECIMAL(15,10) NOT NULL DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
  FOREIGN KEY (shooter_id) REFERENCES shooters(id) ON DELETE CASCADE,
  UNIQUE KEY unique_event_shooter (event_id, shooter_id),
  UNIQUE KEY unique_event_position (event_id, position),
  INDEX idx_event_id (event_id),
  INDEX idx_shooter_id (shooter_id),
  INDEX idx_position (position),
  INDEX idx_points (points)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create rankings view for easy querying
CREATE OR REPLACE VIEW shooter_rankings AS
SELECT 
  s.id as shooter_id,
  s.name as shooter_name,
  s.normalized_name,
  COUNT(r.id) as total_events,
  SUM(r.points) as total_points,
  MIN(r.position) as best_position,
  MAX(e.date) as last_event_date,
  AVG(r.points) as avg_points_per_event
FROM shooters s
LEFT JOIN results r ON s.id = r.shooter_id
LEFT JOIN events e ON r.event_id = e.id
WHERE e.date >= DATE_SUB(CURDATE(), INTERVAL 3 YEAR)
GROUP BY s.id, s.name, s.normalized_name
HAVING total_events > 0
ORDER BY total_points DESC, total_events DESC, best_position ASC;

-- Insert some sample data for testing (optional)
-- Uncomment the following lines if you want sample data

/*
-- Sample events
INSERT IGNORE INTO events (date, location, a_grade_count, competition_name) VALUES
('2024-03-15', 'NSW', 25, 'NSW Kings 2024'),
('2024-04-20', 'VIC', 30, 'VIC Kings 2024'),
('2024-05-18', 'QLD', 28, 'QLD Kings 2024');

-- Sample shooters
INSERT IGNORE INTO shooters (name, normalized_name) VALUES
('John Smith', 'john smith'),
('Sarah Johnson', 'sarah johnson'),
('Michael Brown', 'michael brown'),
('Emma Wilson', 'emma wilson'),
('James Davis', 'james davis');

-- Sample results
INSERT IGNORE INTO results (event_id, shooter_id, position, score, points) VALUES
(1, 1, 1, 398.50, 25),
(1, 2, 2, 397.20, 20),
(1, 3, 3, 396.80, 15),
(1, 4, 4, 395.90, 10),
(1, 5, 5, 394.70, 10),
(2, 2, 1, 399.10, 30),
(2, 1, 2, 398.80, 24),
(2, 4, 3, 397.50, 18),
(2, 3, 4, 396.20, 12),
(2, 5, 5, 395.80, 12);
*/

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_events_date_location ON events(date, location);
CREATE INDEX IF NOT EXISTS idx_results_points_desc ON results(points DESC);
CREATE INDEX IF NOT EXISTS idx_shooters_name_search ON shooters(name(20));

-- Log successful initialization
SELECT 'NRAA Rankings database initialized successfully!' as status;
