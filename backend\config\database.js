const mysql = require('mysql2/promise');

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'mysql',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'nraa_user',
  password: process.env.DB_PASSWORD || 'nraa_password',
  database: process.env.DB_NAME || 'nraa_rankings',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
};

// Create connection pool
const pool = mysql.createPool(dbConfig);

// Test database connection
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('✅ Database connected successfully');
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

// Initialize database tables
async function initializeTables() {
  try {
    const connection = await pool.getConnection();
    
    // Create events table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS events (
        id INT AUTO_INCREMENT PRIMARY KEY,
        date DATE NOT NULL,
        location VARCHAR(50) NOT NULL,
        a_grade_count INT NOT NULL,
        competition_name VARCHAR(255),
        source_url TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_date (date),
        INDEX idx_location (location),
        INDEX idx_location_date (location, date)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    // Create shooters table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS shooters (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        normalized_name VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_normalized_name (normalized_name),
        INDEX idx_name (name),
        INDEX idx_normalized_name (normalized_name)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    // Create results table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS results (
        id INT AUTO_INCREMENT PRIMARY KEY,
        event_id INT NOT NULL,
        shooter_id INT NOT NULL,
        position INT NOT NULL,
        score DECIMAL(10,2),
        points INT NOT NULL DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
        FOREIGN KEY (shooter_id) REFERENCES shooters(id) ON DELETE CASCADE,
        UNIQUE KEY unique_event_shooter (event_id, shooter_id),
        UNIQUE KEY unique_event_position (event_id, position),
        INDEX idx_event_id (event_id),
        INDEX idx_shooter_id (shooter_id),
        INDEX idx_position (position),
        INDEX idx_points (points)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    // Create rankings view for easy querying
    await connection.execute(`
      CREATE OR REPLACE VIEW shooter_rankings AS
      SELECT 
        s.id as shooter_id,
        s.name as shooter_name,
        s.normalized_name,
        COUNT(r.id) as total_events,
        SUM(r.points) as total_points,
        MIN(r.position) as best_position,
        MAX(e.date) as last_event_date,
        AVG(r.points) as avg_points_per_event
      FROM shooters s
      LEFT JOIN results r ON s.id = r.shooter_id
      LEFT JOIN events e ON r.event_id = e.id
      WHERE e.date >= DATE_SUB(CURDATE(), INTERVAL 3 YEAR)
      GROUP BY s.id, s.name, s.normalized_name
      HAVING total_events > 0
      ORDER BY total_points DESC, total_events DESC, best_position ASC
    `);
    
    connection.release();
    console.log('✅ Database tables initialized successfully');
    return true;
  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);
    throw error;
  }
}

// Utility function to normalize shooter names
function normalizeShooterName(name) {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^\w\s]/g, '') // Remove special characters
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
}

// Calculate points based on position and A Grade count
function calculatePoints(position, aGradeCount) {
  if (position === 1) return aGradeCount;
  if (position === 2) return Math.floor(aGradeCount * 0.8);
  if (position === 3) return Math.floor(aGradeCount * 0.6);
  if (position <= 5) return Math.floor(aGradeCount * 0.4);
  if (position <= 10) return Math.floor(aGradeCount * 0.2);
  return 0;
}

// Execute query with error handling
async function executeQuery(query, params = []) {
  try {
    const [rows] = await pool.execute(query, params);
    return rows;
  } catch (error) {
    console.error('Database query error:', error.message);
    console.error('Query:', query);
    console.error('Params:', params);
    throw error;
  }
}

// Get connection from pool
async function getConnection() {
  return await pool.getConnection();
}

module.exports = {
  pool,
  testConnection,
  initializeTables,
  normalizeShooterName,
  calculatePoints,
  executeQuery,
  getConnection
};
