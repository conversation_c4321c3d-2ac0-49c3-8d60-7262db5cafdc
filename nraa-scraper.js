// NRAA Results Auto-Ingestor
// Scrapes Kings Prize results from the NRAA WordPress site

class NRAAScraper {
    constructor() {
        this.baseUrl = 'https://www.results.nraa.com.au';
        this.yearUrls = {
            2025: '/all-kings-prize-results-2025/',
            2024: '/all-kings-prize-results-2024/',
            2023: '/all-kings-prize-results-2023/'
        };
        this.stateMapping = {
            'ACT': 'ACT',
            'NQRA': 'QLD',
            'NSWRA': 'NSW',
            'NTRA': 'NT',
            'QRA': 'QLD',
            'SARA': 'SA',
            'TRA': 'TAS',
            'VRA': 'VIC',
            'WARA': 'WA'
        };
    }

    // Main entry point - scrape all available years
    async scrapeAllYears() {
        const results = [];
        
        for (const [year, url] of Object.entries(this.yearUrls)) {
            try {
                console.log(`Scraping ${year} results...`);
                const yearResults = await this.scrapeYear(year, url);
                results.push(...yearResults);
            } catch (error) {
                console.error(`Failed to scrape ${year}:`, error);
            }
        }
        
        return results;
    }

    // Scrape a specific year's results
    async scrapeYear(year, yearUrl) {
        const fullUrl = this.baseUrl + yearUrl;

        try {
            // Check if we're running in a browser environment with CORS restrictions
            if (typeof window !== 'undefined') {
                throw new Error('CORS_RESTRICTION');
            }

            const response = await fetch(fullUrl);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const html = await response.text();
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // Find all state competition links
            const stateLinks = this.extractStateLinks(doc, year);

            const yearResults = [];
            for (const stateLink of stateLinks) {
                try {
                    const stateResults = await this.scrapeStateCompetition(stateLink);
                    yearResults.push(...stateResults);
                } catch (error) {
                    console.error(`Failed to scrape ${stateLink.state} ${year}:`, error);
                }
            }

            return yearResults;
        } catch (error) {
            if (error.message === 'CORS_RESTRICTION') {
                throw new Error('Browser CORS restrictions prevent direct scraping. Please use the manual import methods or run from a server environment.');
            }
            console.error(`Failed to fetch year page ${year}:`, error);
            throw error;
        }
    }

    // Extract state competition links from year page
    extractStateLinks(doc, year) {
        const links = [];

        // Look for all links on the page
        const allLinks = doc.querySelectorAll('a[href]');

        allLinks.forEach(link => {
            const href = link.getAttribute('href');
            const text = link.textContent.trim();
            const linkText = text.toLowerCase();

            // Skip if no href or if it's not a relative/absolute link to results
            if (!href || (!href.includes('results.nraa.com.au') && !href.startsWith('/'))) {
                return;
            }

            // Look for patterns that indicate state competitions
            // Common patterns: "ACT 2024", "NQRA 2024", "NSW Kings", etc.
            const statePatterns = [
                /^(ACT|NQRA|NSWRA|NTRA|QRA|SARA|TRA|VRA|WARA)\s*(\d{4})?$/i,
                /^(ACT|NSW|NT|QLD|SA|TAS|VIC|WA)\s*(Kings?)?\s*(\d{4})?$/i,
                /(ACT|NQRA|NSWRA|NTRA|QRA|SARA|TRA|VRA|WARA)\s*(\d{4})/i,
                /(ACT|NSW|NT|QLD|SA|TAS|VIC|WA)\s*(Kings?)\s*(\d{4})/i
            ];

            let stateCode = null;
            let detectedYear = null;

            // Try to match state patterns
            for (const pattern of statePatterns) {
                const match = text.match(pattern);
                if (match) {
                    stateCode = match[1].toUpperCase();
                    detectedYear = match[2] || match[3] || year;
                    break;
                }
            }

            // Also check the URL for state indicators
            if (!stateCode) {
                const urlLower = href.toLowerCase();
                const urlStatePatterns = [
                    /\/(act|nqra|nswra|ntra|qra|sara|tra|vra|wara)-/i,
                    /\/(act|nsw|nt|qld|sa|tas|vic|wa)-/i
                ];

                for (const pattern of urlStatePatterns) {
                    const match = href.match(pattern);
                    if (match) {
                        stateCode = match[1].toUpperCase();
                        break;
                    }
                }
            }

            // If we found a state code and the year matches (or is close)
            if (stateCode && (detectedYear == year || Math.abs(detectedYear - year) <= 1)) {
                const mappedState = this.stateMapping[stateCode] || stateCode;

                // Make sure URL is absolute
                const fullUrl = href.startsWith('http') ? href : this.baseUrl + href;

                links.push({
                    state: mappedState,
                    stateCode: stateCode,
                    year: year,
                    url: fullUrl,
                    text: text,
                    originalHref: href
                });
            }
        });

        // Remove duplicates based on URL
        const uniqueLinks = [];
        const seenUrls = new Set();

        links.forEach(link => {
            if (!seenUrls.has(link.url)) {
                seenUrls.add(link.url);
                uniqueLinks.push(link);
            }
        });

        console.log(`Found ${uniqueLinks.length} state competition links for ${year}:`, uniqueLinks);
        return uniqueLinks;
    }

    // Scrape an individual state competition
    async scrapeStateCompetition(stateLink) {
        try {
            const response = await fetch(stateLink.url);
            const html = await response.text();
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            
            // Find the Kings Aggregate section
            const kingsSection = this.findKingsAggregateSection(doc);
            if (!kingsSection) {
                console.warn(`No Kings Aggregate section found for ${stateLink.state} ${stateLink.year}`);
                return [];
            }
            
            // Extract results from the Kings section
            const results = this.extractResultsFromTable(kingsSection, stateLink);
            
            return results;
        } catch (error) {
            console.error(`Failed to scrape state competition ${stateLink.url}:`, error);
            return [];
        }
    }

    // Find the Kings Aggregate section in the page
    findKingsAggregateSection(doc) {
        console.log('Looking for Kings Aggregate sections in collapsible divs...');

        // First, look for collapsible sections with Kings in the title
        const collapsibleSections = doc.querySelectorAll('.collapsible, div[class*="collaps"], div[style*="collaps"]');

        for (const section of collapsibleSections) {
            // Look for headings within this collapsible section
            const headings = section.querySelectorAll('h1, h2, h3, h4, h5, h6');

            for (const heading of headings) {
                const text = heading.textContent.toLowerCase();
                console.log('Found collapsible heading:', text);

                // Look for "Kings" aggregate but exclude "Grand" and daily matches
                if (text.includes('kings') &&
                    text.includes('aggregate') &&
                    !text.includes('grand') &&
                    !text.includes('day 1') &&
                    !text.includes('day 2') &&
                    !text.includes('day 3')) {

                    console.log('Found Kings Aggregate section:', text);

                    // Look for table in this collapsible section or its siblings
                    let table = this.findTableInCollapsibleSection(section);
                    if (table) {
                        console.log('Found table in Kings Aggregate section');
                        return table;
                    }
                }
            }
        }

        // Fallback: look for any heading with "Kings" and "Aggregate"
        const allHeadings = doc.querySelectorAll('h1, h2, h3, h4, h5, h6');

        for (const heading of allHeadings) {
            const text = heading.textContent.toLowerCase();
            if (text.includes('kings') &&
                text.includes('aggregate') &&
                !text.includes('grand') &&
                !text.includes('day')) {

                console.log('Found Kings heading (fallback):', text);

                // Find the associated table
                let current = heading.nextElementSibling;
                while (current && current !== heading.parentElement?.nextElementSibling) {
                    if (current.tagName === 'TABLE') {
                        return current;
                    }
                    // Also check for tables within the next few siblings
                    const table = current.querySelector('table');
                    if (table) {
                        return table;
                    }
                    current = current.nextElementSibling;
                }

                // Also check the parent container for tables
                const parentTable = heading.closest('div')?.querySelector('table');
                if (parentTable) {
                    return parentTable;
                }
            }
        }

        // Final fallback: look for any table with results-like headers
        console.log('Using fallback: looking for any results table...');
        const tables = doc.querySelectorAll('table');
        for (const table of tables) {
            const headers = table.querySelectorAll('th, td');
            const headerText = Array.from(headers).map(h => h.textContent.toLowerCase()).join(' ');

            if (headerText.includes('place') &&
                headerText.includes('name') &&
                (headerText.includes('score') || headerText.includes('total'))) {
                console.log('Found fallback results table');
                return table;
            }
        }

        console.log('No Kings Aggregate section found');
        return null;
    }

    // Helper method to find table within a collapsible section
    findTableInCollapsibleSection(section) {
        // Look for table directly in this section
        let table = section.querySelector('table');
        if (table) return table;

        // Look in the next sibling (content might be in next div)
        let nextSibling = section.nextElementSibling;
        while (nextSibling) {
            table = nextSibling.querySelector('table');
            if (table) return table;

            // If we hit another collapsible section, stop looking
            if (nextSibling.classList.contains('collapsible') ||
                nextSibling.className.includes('collaps')) {
                break;
            }

            nextSibling = nextSibling.nextElementSibling;
        }

        // Look in parent container
        const parent = section.parentElement;
        if (parent) {
            table = parent.querySelector('table');
            if (table) return table;
        }

        return null;
    }

    // Extract results from a results table
    extractResultsFromTable(table, stateLink) {
        const results = [];
        const rows = table.querySelectorAll('tr');
        
        // Find header row to understand column structure
        let headerRow = null;
        let dataStartIndex = 0;
        
        for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            const cells = row.querySelectorAll('th, td');
            const cellTexts = Array.from(cells).map(c => c.textContent.toLowerCase().trim());
            
            if (cellTexts.some(text => text.includes('place') || text.includes('pos')) &&
                cellTexts.some(text => text.includes('name')) &&
                cellTexts.some(text => text.includes('score'))) {
                headerRow = row;
                dataStartIndex = i + 1;
                break;
            }
        }
        
        if (!headerRow) {
            console.warn(`No header row found in table for ${stateLink.state} ${stateLink.year}`);
            return results;
        }
        
        // Map column indices
        const columnMap = this.mapTableColumns(headerRow);
        
        // Extract data rows
        for (let i = dataStartIndex; i < rows.length; i++) {
            const row = rows[i];
            const cells = row.querySelectorAll('td');
            
            if (cells.length < 3) continue; // Skip rows with insufficient data
            
            const result = this.extractRowData(cells, columnMap, stateLink);
            if (result) {
                results.push(result);
            }
        }
        
        return results;
    }

    // Map table columns to data fields
    mapTableColumns(headerRow) {
        const headers = headerRow.querySelectorAll('th, td');
        const columnMap = {};
        
        headers.forEach((header, index) => {
            const text = header.textContent.toLowerCase().trim();
            
            if (text.includes('place') || text.includes('pos')) {
                columnMap.position = index;
            } else if (text.includes('last') && text.includes('name')) {
                columnMap.lastName = index;
            } else if (text.includes('first') || text.includes('preferred')) {
                columnMap.firstName = index;
            } else if (text.includes('name') && !columnMap.lastName) {
                columnMap.fullName = index;
            } else if (text.includes('score')) {
                columnMap.score = index;
            } else if (text.includes('state')) {
                columnMap.state = index;
            } else if (text.includes('club')) {
                columnMap.club = index;
            }
        });
        
        return columnMap;
    }

    // Extract data from a table row
    extractRowData(cells, columnMap, stateLink) {
        try {
            // Get position
            const positionText = cells[columnMap.position]?.textContent.trim();
            const position = parseInt(positionText);
            
            if (isNaN(position) || position < 1) {
                return null; // Skip invalid positions
            }
            
            // Get shooter name
            let shooterName = '';
            if (columnMap.fullName !== undefined) {
                shooterName = cells[columnMap.fullName]?.textContent.trim();
            } else if (columnMap.lastName !== undefined && columnMap.firstName !== undefined) {
                const lastName = cells[columnMap.lastName]?.textContent.trim() || '';
                const firstName = cells[columnMap.firstName]?.textContent.trim() || '';
                shooterName = `${firstName} ${lastName}`.trim();
            }
            
            if (!shooterName) {
                return null; // Skip rows without names
            }
            
            // Get score (optional for our ranking system)
            const score = cells[columnMap.score]?.textContent.trim();
            
            return {
                shooter: shooterName,
                position: position,
                score: score,
                state: stateLink.state,
                year: stateLink.year,
                competition: `${stateLink.state} Kings ${stateLink.year}`,
                sourceUrl: stateLink.url
            };
        } catch (error) {
            console.error('Error extracting row data:', error);
            return null;
        }
    }

    // Convert scraped results to our event format
    convertToEventFormat(scrapedResults) {
        // Group results by competition
        const competitions = {};
        
        scrapedResults.forEach(result => {
            const key = `${result.state}-${result.year}`;
            if (!competitions[key]) {
                competitions[key] = {
                    date: `${result.year}-01-01`, // Default date, can be refined
                    location: result.state,
                    aGradeCount: 0, // Will be calculated
                    results: [],
                    sourceUrl: result.sourceUrl,
                    competition: result.competition
                };
            }
            
            competitions[key].results.push({
                shooter: result.shooter,
                position: result.position,
                score: result.score
            });
        });
        
        // Calculate A Grade counts and sort results
        Object.values(competitions).forEach(comp => {
            comp.results.sort((a, b) => a.position - b.position);
            comp.aGradeCount = comp.results.length; // Use total participants as A Grade count
        });
        
        return Object.values(competitions);
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NRAAScraper;
}

// Global instance for browser use
const nraaScraper = new NRAAScraper();
