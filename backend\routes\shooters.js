const express = require('express');
const { executeQuery } = require('../config/database');

const router = express.Router();

// GET /api/shooters - Get all shooters
router.get('/', async (req, res) => {
  try {
    const { limit = 100, offset = 0, search } = req.query;
    
    let query = 'SELECT id, name, normalized_name, created_at FROM shooters';
    const params = [];
    
    if (search) {
      query += ' WHERE name LIKE ? OR normalized_name LIKE ?';
      params.push(`%${search}%`, `%${search}%`);
    }
    
    query += ' ORDER BY name ASC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));
    
    const shooters = await executeQuery(query, params);
    
    res.json({
      shooters,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });
  } catch (error) {
    console.error('Error fetching shooters:', error);
    res.status(500).json({ error: 'Failed to fetch shooters' });
  }
});

// GET /api/shooters/autocomplete - Get shooter names for autocomplete
router.get('/autocomplete', async (req, res) => {
  try {
    const { q } = req.query;
    
    if (!q || q.length < 2) {
      return res.json([]);
    }
    
    const shooters = await executeQuery(`
      SELECT DISTINCT name
      FROM shooters
      WHERE name LIKE ?
      ORDER BY name ASC
      LIMIT 20
    `, [`%${q}%`]);
    
    res.json(shooters.map(s => s.name));
  } catch (error) {
    console.error('Error fetching shooter autocomplete:', error);
    res.status(500).json({ error: 'Failed to fetch shooter suggestions' });
  }
});

// GET /api/shooters/:id - Get specific shooter
router.get('/:id', async (req, res) => {
  try {
    const shooterId = parseInt(req.params.id);
    
    const shooters = await executeQuery(
      'SELECT * FROM shooters WHERE id = ?',
      [shooterId]
    );
    
    if (shooters.length === 0) {
      return res.status(404).json({ error: 'Shooter not found' });
    }
    
    res.json(shooters[0]);
  } catch (error) {
    console.error('Error fetching shooter:', error);
    res.status(500).json({ error: 'Failed to fetch shooter' });
  }
});

module.exports = router;
