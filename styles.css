body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: relative;
    margin-bottom: 30px;
    border-radius: 8px;
}

header h1 {
    margin: 0 0 0.5rem 0;
    font-size: 2.5rem;
    font-weight: 300;
}

header p {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    opacity: 0.9;
}

.backend-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    background-color: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.backend-status.online {
    background-color: rgba(76, 175, 80, 0.2);
    border-color: rgba(76, 175, 80, 0.5);
    color: #4caf50;
}

.backend-status.offline {
    background-color: rgba(244, 67, 54, 0.2);
    border-color: rgba(244, 67, 54, 0.5);
    color: #f44336;
}

.controls {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
    align-items: center;
}

#search {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

button {
    padding: 8px 16px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
}

tr:hover {
    background-color: #f5f5f5;
}

/* Tab Navigation */
.tab-container {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 2px solid #ddd;
}

.tab-btn {
    padding: 12px 24px;
    background-color: transparent;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-size: 16px;
    color: #666;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    background-color: #f8f9fa;
    color: #333;
}

.tab-btn.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background-color: #f8f9fa;
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Score Entry Styles */
.score-entry-container {
    max-width: 1000px;
    margin: 0 auto;
}

.event-form, .results-form, .current-results {
    background-color: white;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.event-form h3, .results-form h3, .current-results h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}

.form-row {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.form-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.form-group label {
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

.form-group input, .form-group select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group input:focus, .form-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

/* Position Input Group */
.position-input-group {
    display: flex;
    gap: 5px;
    align-items: center;
}

.position-input-group input {
    flex: 1;
}

.auto-increment-btn {
    padding: 8px 12px;
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    white-space: nowrap;
    transition: all 0.3s ease;
    min-width: 70px;
}

.auto-increment-btn:hover {
    background-color: #5a6268;
}

.auto-increment-btn.active {
    background-color: #28a745;
}

.auto-increment-btn.active:hover {
    background-color: #218838;
}

/* Results List */
.results-list {
    min-height: 100px;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    background-color: #f9f9f9;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 5px;
    background-color: white;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}

.result-item:last-child {
    margin-bottom: 0;
}

.result-info {
    display: flex;
    gap: 15px;
    align-items: center;
}

.result-position {
    font-weight: bold;
    color: #007bff;
    min-width: 30px;
}

.result-name {
    font-weight: 500;
}

.result-points {
    color: #666;
    font-size: 14px;
}

.remove-btn {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.remove-btn:hover {
    background-color: #c82333;
}

.no-results {
    text-align: center;
    color: #666;
    font-style: italic;
    margin: 20px 0;
}

/* Progress Indicator */
.progress-indicator {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 5px;
}

.progress-fill {
    height: 100%;
    background-color: #28a745;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    justify-content: flex-end;
}

#save-event-btn {
    background-color: #28a745;
}

#save-event-btn:hover:not(:disabled) {
    background-color: #218838;
}

#save-event-btn:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

#clear-event-btn {
    background-color: #6c757d;
}

#clear-event-btn:hover {
    background-color: #5a6268;
}

/* Events List */
.events-container {
    max-width: 1000px;
    margin: 0 auto;
}

.event-card {
    background-color: white;
    padding: 20px;
    margin-bottom: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #007bff;
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.event-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.event-date {
    color: #666;
    font-size: 14px;
}

.event-details {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.event-detail {
    display: flex;
    flex-direction: column;
}

.event-detail-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    margin-bottom: 2px;
}

.event-detail-value {
    font-weight: bold;
    color: #333;
}

.event-results {
    margin-top: 15px;
}

.event-results-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
}

.event-results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
}

.event-result-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 14px;
}

.event-result-position {
    font-weight: bold;
    color: #007bff;
}

/* Scraper Integration Styles */
.scraper-controls {
    background-color: white;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #17a2b8;
}

.scraper-controls h3 {
    margin-top: 0;
    color: #17a2b8;
    border-bottom: 2px solid #17a2b8;
    padding-bottom: 5px;
}

.scraper-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 15px;
}

.scraper-btn {
    padding: 10px 20px;
    background-color: #17a2b8;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.scraper-btn:hover {
    background-color: #138496;
}

.scraper-btn.test {
    background-color: #ffc107;
    color: #212529;
}

.scraper-btn.test:hover {
    background-color: #e0a800;
}

.scraper-btn:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-content h3 {
    margin-top: 0;
    color: #333;
    text-align: center;
}

.progress-container {
    margin: 20px 0;
}

.progress-details {
    margin-top: 15px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    max-height: 200px;
    overflow-y: auto;
    font-size: 12px;
    color: #666;
}

#cancel-scraping-btn {
    width: 100%;
    margin-top: 20px;
    background-color: #dc3545;
}

#cancel-scraping-btn:hover {
    background-color: #c82333;
}

/* URL Input Styles */
.url-input-group {
    display: flex;
    gap: 10px;
    margin-top: 10px;
    align-items: center;
}

.url-input-group input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.url-input-group button {
    padding: 8px 16px;
    white-space: nowrap;
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        align-items: stretch;
    }

    .form-group {
        min-width: auto;
    }

    .tab-container {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1;
        min-width: 120px;
    }

    .event-details {
        flex-direction: column;
        gap: 10px;
    }

    .event-results-grid {
        grid-template-columns: 1fr;
    }

    .scraper-buttons {
        flex-direction: column;
    }

    .url-input-group {
        flex-direction: column;
    }

    .modal-content {
        margin: 20px;
        width: calc(100% - 40px);
    }
}
