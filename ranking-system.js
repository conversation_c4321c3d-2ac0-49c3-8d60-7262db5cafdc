// Kings Prize Ranking System
class RankingSystem {
    constructor() {
        this.events = [];
        this.shooters = new Map();
    }

    // Add an event to the system
    addEvent(eventData) {
        const event = {
            id: Date.now() + Math.random(),
            date: new Date(eventData.date),
            location: eventData.location,
            aGradeCount: eventData.aGradeCount,
            results: eventData.results
        };
        
        this.events.push(event);
        this.updateShooterRankings();
    }

    // Calculate points based on position and A Grade count
    calculatePoints(position, aGradeCount) {
        if (position === 1) return aGradeCount;
        if (position === 2) return Math.floor(aGradeCount * 0.8);
        if (position === 3) return Math.floor(aGradeCount * 0.6);
        if (position <= 5) return Math.floor(aGradeCount * 0.4);
        if (position <= 10) return Math.floor(aGradeCount * 0.2);
        return 0;
    }

    // Update shooter rankings based on all events
    updateShooterRankings() {
        this.shooters.clear();
        
        // Get events from last 3 years
        const threeYearsAgo = new Date();
        threeYearsAgo.setFullYear(threeYearsAgo.getFullYear() - 3);
        
        const recentEvents = this.events.filter(event => event.date >= threeYearsAgo);
        
        recentEvents.forEach(event => {
            event.results.forEach(result => {
                const shooterName = result.shooter;
                const points = this.calculatePoints(result.position, event.aGradeCount);
                
                if (!this.shooters.has(shooterName)) {
                    this.shooters.set(shooterName, {
                        name: shooterName,
                        totalPoints: 0,
                        events: [],
                        bestResult: null
                    });
                }
                
                const shooter = this.shooters.get(shooterName);
                shooter.totalPoints += points;
                shooter.events.push({
                    date: event.date,
                    location: event.location,
                    position: result.position,
                    points: points
                });
                
                // Update best result
                if (!shooter.bestResult || result.position < shooter.bestResult.position) {
                    shooter.bestResult = {
                        position: result.position,
                        location: event.location,
                        date: event.date
                    };
                }
            });
        });
    }

    // Get sorted rankings
    getRankings() {
        const rankings = Array.from(this.shooters.values())
            .sort((a, b) => b.totalPoints - a.totalPoints)
            .map((shooter, index) => ({
                rank: index + 1,
                ...shooter
            }));
        
        return rankings;
    }

    // Search shooters by name
    searchShooters(query) {
        const rankings = this.getRankings();
        if (!query) return rankings;

        return rankings.filter(shooter =>
            shooter.name.toLowerCase().includes(query.toLowerCase())
        );
    }

    // Get all unique shooter names for autocomplete
    getAllShooterNames() {
        const names = new Set();
        this.events.forEach(event => {
            event.results.forEach(result => {
                names.add(result.shooter);
            });
        });
        return Array.from(names).sort();
    }

    // Get all events
    getAllEvents() {
        return this.events.sort((a, b) => b.date - a.date);
    }

    // Delete an event
    deleteEvent(eventId) {
        this.events = this.events.filter(event => event.id !== eventId);
        this.updateShooterRankings();
    }

    // Validate event data
    validateEventData(eventData) {
        const errors = [];

        if (!eventData.date) {
            errors.push('Event date is required');
        }

        if (!eventData.location) {
            errors.push('Event location is required');
        }

        if (!eventData.aGradeCount || eventData.aGradeCount < 1) {
            errors.push('A Grade count must be a positive number');
        }

        if (!eventData.results || eventData.results.length === 0) {
            errors.push('At least one shooter result is required');
        }

        if (eventData.results) {
            // Check for duplicate positions
            const positions = eventData.results.map(r => r.position);
            const uniquePositions = new Set(positions);
            if (positions.length !== uniquePositions.size) {
                errors.push('Duplicate positions are not allowed');
            }

            // Check for duplicate shooters
            const shooters = eventData.results.map(r => r.shooter.toLowerCase());
            const uniqueShooters = new Set(shooters);
            if (shooters.length !== uniqueShooters.size) {
                errors.push('Duplicate shooters are not allowed');
            }

            // Validate each result
            eventData.results.forEach((result, index) => {
                if (!result.shooter || result.shooter.trim() === '') {
                    errors.push(`Shooter name is required for result ${index + 1}`);
                }
                if (!result.position || result.position < 1) {
                    errors.push(`Valid position is required for result ${index + 1}`);
                }
            });
        }

        return errors;
    }
}

// Global ranking system instance
const rankingSystem = new RankingSystem();

// Display functions
function displayRankings(rankings = null) {
    const tbody = document.getElementById('rankings-body');
    const displayData = rankings || rankingSystem.getRankings();

    tbody.innerHTML = '';

    if (displayData.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="5" style="text-align: center; padding: 40px; color: #666; font-style: italic;">
                No rankings available yet. Add some events using the Score Entry tab to get started!
            </td>
        `;
        tbody.appendChild(row);
        return;
    }

    displayData.forEach(shooter => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${shooter.rank}</td>
            <td>${shooter.name}</td>
            <td>${shooter.totalPoints}</td>
            <td>${shooter.events.length}</td>
            <td>${shooter.bestResult ? `${shooter.bestResult.position} (${shooter.bestResult.location})` : 'N/A'}</td>
        `;
        tbody.appendChild(row);
    });
}

// Score Entry Management
class ScoreEntryManager {
    constructor() {
        this.currentEventResults = [];
        this.currentEventData = {};
        this.autoIncrementPosition = true;
    }

    // Get the next suggested position
    getNextPosition() {
        if (this.currentEventResults.length === 0) {
            return 1;
        }

        // Find the highest position and add 1
        const maxPosition = Math.max(...this.currentEventResults.map(r => r.position));
        return maxPosition + 1;
    }

    // Check if we've reached the A Grade count limit
    isPositionLimitReached() {
        const aGradeCount = parseInt(document.getElementById('a-grade-count').value) || 0;
        return this.currentEventResults.length >= aGradeCount;
    }

    // Update the position field with next suggested position
    updatePositionField() {
        const positionField = document.getElementById('shooter-position');
        if (this.autoIncrementPosition && positionField) {
            const nextPos = this.getNextPosition();
            const aGradeCount = parseInt(document.getElementById('a-grade-count').value) || 0;

            // Only auto-fill if we haven't exceeded A Grade count
            if (aGradeCount === 0 || nextPos <= aGradeCount) {
                positionField.value = nextPos;
            } else {
                positionField.value = '';
                positionField.placeholder = `Position ${nextPos} (beyond A Grade count)`;
            }
        }
    }

    // Toggle auto-increment feature
    toggleAutoIncrement() {
        this.autoIncrementPosition = !this.autoIncrementPosition;
        this.updatePositionField();
        this.updateAutoIncrementButton();
    }

    // Update the auto-increment button appearance
    updateAutoIncrementButton() {
        const btn = document.getElementById('auto-increment-btn');
        if (btn) {
            btn.textContent = this.autoIncrementPosition ? '🔢 Auto' : '✋ Manual';
            btn.title = this.autoIncrementPosition ?
                'Auto-increment positions (click to disable)' :
                'Manual position entry (click to enable auto-increment)';
            btn.classList.toggle('active', this.autoIncrementPosition);
        }
    }

    // Add shooter to current event
    addShooter(name, position) {
        // Check if shooter already exists
        const existingIndex = this.currentEventResults.findIndex(r =>
            r.shooter.toLowerCase() === name.toLowerCase()
        );

        if (existingIndex !== -1) {
            // Update existing shooter
            this.currentEventResults[existingIndex].position = position;
        } else {
            // Add new shooter
            this.currentEventResults.push({
                shooter: name.trim(),
                position: parseInt(position)
            });
        }

        // Sort by position
        this.currentEventResults.sort((a, b) => a.position - b.position);
        this.updateResultsDisplay();

        // Update position field for next entry if auto-increment is enabled
        if (this.autoIncrementPosition) {
            this.updatePositionField();
        }
    }

    // Remove shooter from current event
    removeShooter(index) {
        this.currentEventResults.splice(index, 1);
        this.updateResultsDisplay();

        // Update position field after removal if auto-increment is enabled
        if (this.autoIncrementPosition) {
            this.updatePositionField();
        }
    }

    // Clear all results
    clearResults() {
        this.currentEventResults = [];
        this.currentEventData = {};
        this.updateResultsDisplay();
        this.clearForm();

        // Reset position field to 1 if auto-increment is enabled
        if (this.autoIncrementPosition) {
            this.updatePositionField();
        }
    }

    // Update the results display
    updateResultsDisplay() {
        const container = document.getElementById('current-results-list');
        const saveBtn = document.getElementById('save-event-btn');
        const aGradeCount = parseInt(document.getElementById('a-grade-count').value) || 0;

        if (this.currentEventResults.length === 0) {
            container.innerHTML = '<p class="no-results">No shooters added yet</p>';
            saveBtn.disabled = true;
        } else {
            // Add progress indicator
            let progressHtml = '';
            if (aGradeCount > 0) {
                const progress = Math.min(this.currentEventResults.length, aGradeCount);
                const progressPercent = (progress / aGradeCount) * 100;
                progressHtml = `
                    <div class="progress-indicator">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${progressPercent}%"></div>
                        </div>
                        <span class="progress-text">${progress} of ${aGradeCount} A Grade shooters added</span>
                    </div>
                `;
            }

            const resultsHtml = this.currentEventResults.map((result, index) => {
                const points = rankingSystem.calculatePoints(result.position, aGradeCount);

                return `
                    <div class="result-item">
                        <div class="result-info">
                            <span class="result-position">${result.position}</span>
                            <span class="result-name">${result.shooter}</span>
                            <span class="result-points">${points} points</span>
                        </div>
                        <button class="remove-btn" onclick="scoreEntry.removeShooter(${index})">Remove</button>
                    </div>
                `;
            }).join('');

            container.innerHTML = progressHtml + resultsHtml;
            saveBtn.disabled = false;
        }
    }

    // Save the current event
    saveEvent() {
        const eventData = {
            date: document.getElementById('event-date').value,
            location: document.getElementById('event-location').value,
            aGradeCount: parseInt(document.getElementById('a-grade-count').value),
            results: [...this.currentEventResults]
        };

        // Validate the event data
        const errors = rankingSystem.validateEventData(eventData);
        if (errors.length > 0) {
            alert('Please fix the following errors:\n\n' + errors.join('\n'));
            return false;
        }

        // Add the event
        rankingSystem.addEvent(eventData);

        // Clear the form
        this.clearResults();

        // Update displays
        displayRankings();
        displayEvents();

        // Show success message
        alert('Event saved successfully!');

        // Switch to rankings tab
        switchTab('rankings');

        return true;
    }

    // Clear the form
    clearForm() {
        document.getElementById('event-date').value = '';
        document.getElementById('event-location').value = '';
        document.getElementById('a-grade-count').value = '';
        document.getElementById('shooter-name').value = '';
        document.getElementById('shooter-position').value = '';
        document.getElementById('shooter-position').placeholder = 'Final position';

        // Reset position to 1 if auto-increment is enabled
        if (this.autoIncrementPosition) {
            this.updatePositionField();
        }
    }

    // Update shooter suggestions
    updateShooterSuggestions() {
        const datalist = document.getElementById('shooter-suggestions');
        const shooterNames = rankingSystem.getAllShooterNames();

        datalist.innerHTML = shooterNames.map(name =>
            `<option value="${name}">`
        ).join('');
    }
}

// Global score entry manager
const scoreEntry = new ScoreEntryManager();

// Tab Management
function switchTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab content
    document.getElementById(`${tabName}-tab`).classList.add('active');

    // Add active class to selected tab button
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // Update displays based on active tab
    if (tabName === 'events') {
        displayEvents();
    } else if (tabName === 'score-entry') {
        scoreEntry.updateShooterSuggestions();
    }
}

// Display events
function displayEvents() {
    const container = document.getElementById('events-list');
    const events = rankingSystem.getAllEvents();

    if (events.length === 0) {
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #666; font-style: italic; background-color: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h3 style="margin-top: 0;">No events recorded yet</h3>
                <p>Use the Score Entry tab to add your first event and start building the rankings!</p>
            </div>
        `;
        return;
    }

    container.innerHTML = events.map(event => {
        const formattedDate = event.date.toLocaleDateString();
        const topResults = event.results.slice(0, 5); // Show top 5 results

        return `
            <div class="event-card">
                <div class="event-header">
                    <div class="event-title">${event.location} Kings Prize</div>
                    <div class="event-date">${formattedDate}</div>
                </div>
                <div class="event-details">
                    <div class="event-detail">
                        <div class="event-detail-label">Location</div>
                        <div class="event-detail-value">${event.location}</div>
                    </div>
                    <div class="event-detail">
                        <div class="event-detail-label">A Grade Count</div>
                        <div class="event-detail-value">${event.aGradeCount}</div>
                    </div>
                    <div class="event-detail">
                        <div class="event-detail-label">Total Shooters</div>
                        <div class="event-detail-value">${event.results.length}</div>
                    </div>
                </div>
                <div class="event-results">
                    <div class="event-results-title">Top Results:</div>
                    <div class="event-results-grid">
                        ${topResults.map(result => {
                            const points = rankingSystem.calculatePoints(result.position, event.aGradeCount);
                            return `
                                <div class="event-result-item">
                                    <span>${result.position}. ${result.shooter}</span>
                                    <span class="event-result-position">${points} pts</span>
                                </div>
                            `;
                        }).join('')}
                    </div>
                    ${event.results.length > 5 ? `<p style="margin-top: 10px; color: #666; font-size: 14px;">... and ${event.results.length - 5} more</p>` : ''}
                </div>
            </div>
        `;
    }).join('');
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Load sample data on page load
    if (typeof loadSampleData === 'function') {
        loadSampleData();
    }

    // Tab navigation
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            switchTab(tabName);
        });
    });

    // Rankings tab functionality
    document.getElementById('refresh-btn').addEventListener('click', function() {
        displayRankings();
    });

    document.getElementById('search').addEventListener('input', function(e) {
        const query = e.target.value;
        const filteredRankings = rankingSystem.searchShooters(query);
        displayRankings(filteredRankings);
    });

    // Score entry functionality
    document.getElementById('add-shooter-btn').addEventListener('click', function() {
        const name = document.getElementById('shooter-name').value.trim();
        const position = document.getElementById('shooter-position').value;

        if (!name) {
            alert('Please enter a shooter name');
            return;
        }

        if (!position || position < 1) {
            alert('Please enter a valid position');
            return;
        }

        scoreEntry.addShooter(name, position);

        // Clear the name field
        document.getElementById('shooter-name').value = '';

        // Clear position field only if not auto-incrementing
        if (!scoreEntry.autoIncrementPosition) {
            document.getElementById('shooter-position').value = '';
        }

        // Focus back to name field
        document.getElementById('shooter-name').focus();
    });

    // Allow Enter key to add shooter
    document.getElementById('shooter-position').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            document.getElementById('add-shooter-btn').click();
        }
    });

    document.getElementById('shooter-name').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            document.getElementById('shooter-position').focus();
        }
    });

    // Save event button
    document.getElementById('save-event-btn').addEventListener('click', function() {
        scoreEntry.saveEvent();
    });

    // Clear event button
    document.getElementById('clear-event-btn').addEventListener('click', function() {
        if (confirm('Are you sure you want to clear all data? This cannot be undone.')) {
            scoreEntry.clearResults();
        }
    });

    // Auto-increment toggle button
    document.getElementById('auto-increment-btn').addEventListener('click', function() {
        scoreEntry.toggleAutoIncrement();
    });

    // Update results display and position field when A Grade count changes
    document.getElementById('a-grade-count').addEventListener('input', function() {
        scoreEntry.updateResultsDisplay();
        if (scoreEntry.autoIncrementPosition) {
            scoreEntry.updatePositionField();
        }
    });

    // Initialize shooter suggestions and auto-increment button
    scoreEntry.updateShooterSuggestions();
    scoreEntry.updateAutoIncrementButton();
    scoreEntry.updatePositionField();
});
