// Test script for the new points calculation system
// Formula: points = aGradeCount / (2^(position-1))

// Import the calculatePoints function from database.js
const path = require('path');
const { calculatePoints } = require('./backend/config/database.js');

// Test cases for the new points calculation system
function testPointsCalculation() {
    console.log('Testing new points calculation system...\n');
    
    // Test case 1: 45 shooters (as mentioned in the example)
    console.log('Test Case 1: 45 shooters');
    console.log('1st place:', calculatePoints(1, 45), 'points (expected: 45)');
    console.log('2nd place:', calculatePoints(2, 45), 'points (expected: 22.5)');
    console.log('3rd place:', calculatePoints(3, 45), 'points (expected: 11.25)');
    console.log('4th place:', calculatePoints(4, 45), 'points (expected: 5.625)');
    console.log('5th place:', calculatePoints(5, 45), 'points (expected: 2.8125)');
    console.log('10th place:', calculatePoints(10, 45), 'points (expected: 0.087890625)');
    console.log('20th place:', calculatePoints(20, 45), 'points (expected: 0.0000858307)');
    console.log('45th place:', calculatePoints(45, 45), 'points (expected: 0.0000000000254)');
    console.log();
    
    // Test case 2: 30 shooters
    console.log('Test Case 2: 30 shooters');
    console.log('1st place:', calculatePoints(1, 30), 'points (expected: 30)');
    console.log('2nd place:', calculatePoints(2, 30), 'points (expected: 15)');
    console.log('3rd place:', calculatePoints(3, 30), 'points (expected: 7.5)');
    console.log('4th place:', calculatePoints(4, 30), 'points (expected: 3.75)');
    console.log('30th place:', calculatePoints(30, 30), 'points');
    console.log();
    
    // Test case 3: 10 shooters
    console.log('Test Case 3: 10 shooters');
    console.log('1st place:', calculatePoints(1, 10), 'points (expected: 10)');
    console.log('2nd place:', calculatePoints(2, 10), 'points (expected: 5)');
    console.log('3rd place:', calculatePoints(3, 10), 'points (expected: 2.5)');
    console.log('10th place:', calculatePoints(10, 10), 'points');
    console.log();
    
    // Test edge cases
    console.log('Edge Cases:');
    console.log('Invalid position (0):', calculatePoints(0, 45), 'points (expected: 0)');
    console.log('Invalid aGradeCount (0):', calculatePoints(1, 0), 'points (expected: 0)');
    console.log('Negative position:', calculatePoints(-1, 45), 'points (expected: 0)');
    console.log('Negative aGradeCount:', calculatePoints(1, -45), 'points (expected: 0)');
    
    console.log('\nTest completed!');
}

// Manual calculation function for verification
function manualCalculatePoints(position, aGradeCount) {
    if (position < 1 || aGradeCount < 1) return 0;
    
    const points = aGradeCount / Math.pow(2, position - 1);
    return Math.round(points * 10000000000) / 10000000000;
}

// Verify our manual calculation matches the imported function
function verifyCalculation() {
    console.log('\nVerifying calculation consistency...');
    
    const testCases = [
        [1, 45], [2, 45], [3, 45], [4, 45], [5, 45],
        [1, 30], [2, 30], [3, 30],
        [1, 10], [2, 10], [10, 10]
    ];
    
    let allMatch = true;
    
    testCases.forEach(([position, aGradeCount]) => {
        const imported = calculatePoints(position, aGradeCount);
        const manual = manualCalculatePoints(position, aGradeCount);
        
        if (imported !== manual) {
            console.log(`MISMATCH: Position ${position}, A Grade ${aGradeCount}`);
            console.log(`  Imported: ${imported}`);
            console.log(`  Manual: ${manual}`);
            allMatch = false;
        }
    });
    
    if (allMatch) {
        console.log('✓ All calculations match!');
    } else {
        console.log('✗ Some calculations do not match!');
    }
}

// Run the tests
if (require.main === module) {
    testPointsCalculation();
    verifyCalculation();
}

module.exports = { testPointsCalculation, verifyCalculation, manualCalculatePoints };
