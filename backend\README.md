# NRAA Rankings Backend

Node.js/Express API with MySQL database for the NRAA Rankings System.

## Features

- **RESTful API** for events, shooters, and rankings
- **MySQL database** with proper relationships and indexes
- **Automatic point calculation** based on position and A Grade count
- **3-year rolling rankings** with real-time updates
- **Bulk import support** for scraped data
- **Data validation** and duplicate detection
- **Health checks** and monitoring

## API Endpoints

### Events
- `GET /api/events` - List all events
- `GET /api/events/:id` - Get specific event with results
- `POST /api/events` - Create new event
- `PUT /api/events/:id` - Update event
- `DELETE /api/events/:id` - Delete event

### Rankings
- `GET /api/rankings` - Get current rankings
- `GET /api/rankings/shooter/:id` - Get shooter ranking details
- `GET /api/rankings/stats` - Get ranking statistics
- `GET /api/rankings/history/:shooterId` - Get shooter's ranking history

### Shooters
- `GET /api/shooters` - List all shooters
- `GET /api/shooters/autocomplete` - Autocomplete suggestions
- `GET /api/shooters/:id` - Get specific shooter

### Import
- `POST /api/import/bulk` - Bulk import events
- `POST /api/import/validate` - Validate import data
- `GET /api/import/stats` - Get import statistics

### Health
- `GET /api/health` - Health check endpoint

## Database Schema

### Events Table
```sql
CREATE TABLE events (
  id INT AUTO_INCREMENT PRIMARY KEY,
  date DATE NOT NULL,
  location VARCHAR(50) NOT NULL,
  a_grade_count INT NOT NULL,
  competition_name VARCHAR(255),
  source_url TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Shooters Table
```sql
CREATE TABLE shooters (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  normalized_name VARCHAR(255) NOT NULL UNIQUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Results Table
```sql
CREATE TABLE results (
  id INT AUTO_INCREMENT PRIMARY KEY,
  event_id INT NOT NULL,
  shooter_id INT NOT NULL,
  position INT NOT NULL,
  score DECIMAL(10,2),
  points INT NOT NULL DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
  FOREIGN KEY (shooter_id) REFERENCES shooters(id) ON DELETE CASCADE
);
```

## Point Calculation

Points are automatically calculated based on position and A Grade count:

- **1st place**: A Grade count points
- **2nd place**: 80% of A Grade count
- **3rd place**: 60% of A Grade count
- **4th-5th place**: 40% of A Grade count
- **6th-10th place**: 20% of A Grade count
- **11th+ place**: 0 points

## Environment Variables

```bash
# Database Configuration
DB_HOST=mysql
DB_PORT=3306
DB_USER=nraa_user
DB_PASSWORD=nraa_password
DB_NAME=nraa_rankings

# Server Configuration
PORT=3001
NODE_ENV=production
FRONTEND_URL=http://localhost:8080
```

## Development

```bash
# Install dependencies
npm install

# Initialize database
npm run init-db

# Start development server
npm run dev

# Start production server
npm start
```

## Docker Deployment

The backend is automatically deployed with Docker Compose:

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs api

# Access MySQL
docker-compose exec mysql mysql -u nraa_user -p nraa_rankings
```

## API Usage Examples

### Create Event
```javascript
POST /api/events
{
  "date": "2024-03-15",
  "location": "NSW",
  "aGradeCount": 25,
  "competitionName": "NSW Kings 2024",
  "results": [
    { "shooter": "John Smith", "position": 1, "score": 398.5 },
    { "shooter": "Jane Doe", "position": 2, "score": 397.2 }
  ]
}
```

### Get Rankings
```javascript
GET /api/rankings?limit=50&search=smith
{
  "rankings": [
    {
      "shooter_id": 1,
      "shooter_name": "John Smith",
      "total_events": 5,
      "total_points": 85,
      "best_position": 1,
      "rank_position": 1
    }
  ]
}
```

### Bulk Import
```javascript
POST /api/import/bulk
{
  "events": [
    {
      "date": "2024-03-15",
      "location": "NSW",
      "aGradeCount": 25,
      "results": [...]
    }
  ]
}
```

## Monitoring

- Health check: `GET /api/health`
- Database status included in health response
- Docker health checks configured
- Automatic restart on failure
