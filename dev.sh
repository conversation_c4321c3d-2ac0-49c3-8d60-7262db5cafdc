#!/bin/bash

# Development script for NRAA EMMS Grading application

case "$1" in
    "start")
        echo "Starting the application..."
        docker-compose up -d
        echo "Application started at http://localhost:8080"
        ;;
    "stop")
        echo "Stopping the application..."
        docker-compose down
        echo "Application stopped."
        ;;
    "restart")
        echo "Restarting the application..."
        docker-compose down
        docker-compose up -d
        echo "Application restarted at http://localhost:8080"
        ;;
    "build")
        echo "Building and starting the application..."
        docker-compose up --build -d
        echo "Application built and started at http://localhost:8080"
        ;;
    "logs")
        echo "Showing application logs..."
        docker-compose logs -f
        ;;
    "dev")
        echo "Starting development server with hot reload..."
        docker-compose --profile dev up
        echo "Development server started at http://localhost:3000"
        ;;
    "status")
        echo "Application status:"
        docker-compose ps
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|build|logs|dev|status}"
        echo ""
        echo "Commands:"
        echo "  start   - Start the application"
        echo "  stop    - Stop the application"
        echo "  restart - Restart the application"
        echo "  build   - Build and start the application"
        echo "  logs    - Show application logs"
        echo "  dev     - Start development server with hot reload"
        echo "  status  - Show application status"
        exit 1
        ;;
esac
