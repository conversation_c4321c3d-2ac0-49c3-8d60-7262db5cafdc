// Import Helper Functions for UI Integration

// Import history tracking
let importHistory = [];

// Test scraping function for UI
async function testScrapingFunction() {
    try {
        addToImportHistory('Testing scraper with sample data...');
        
        // Use mock data for testing
        const mockEvents = scraperIntegration.getMockTestData('2024', 'NQRA');
        
        // Import the mock event
        const importResults = await scraperIntegration.importEvents(mockEvents);
        
        addToImportHistory(`Test completed: ${importResults.imported} events imported, ${importResults.errors} errors`);
        
        alert(`Test Import Successful!\n\nImported: ${importResults.imported} events\nErrors: ${importResults.errors}\n\nCheck the Rankings tab to see the test data.`);
        
        // Switch to rankings tab to show results
        switchTab('rankings');
        
    } catch (error) {
        console.error('Test scraping failed:', error);
        addToImportHistory(`Test failed: ${error.message}`);

        if (error.message.includes('CORS')) {
            scraperIntegration.showCORSError();
        } else {
            alert('Test failed: ' + error.message);
        }
    }
}

// Scrape specific URL function for UI
async function scrapeSpecificUrlFunction() {
    const urlInput = document.getElementById('specific-url');
    const url = urlInput.value.trim();
    
    if (!url) {
        alert('Please enter a URL to scrape');
        return;
    }
    
    if (!url.includes('results.nraa.com.au')) {
        alert('Please enter a valid NRAA results URL');
        return;
    }
    
    try {
        addToImportHistory(`Scraping URL: ${url}`);
        
        const event = await scraperIntegration.scrapeSpecificUrl(url);
        
        if (event) {
            const importResults = await scraperIntegration.importEvents([event]);
            
            addToImportHistory(`URL import completed: ${importResults.imported} events imported, ${importResults.errors} errors`);
            
            alert(`Import Successful!\n\nImported: ${importResults.imported} events\nSkipped: ${importResults.skipped}\nErrors: ${importResults.errors}`);
            
            // Clear the URL input
            urlInput.value = '';
            
            // Switch to rankings tab
            switchTab('rankings');
        } else {
            throw new Error('No event data found at the specified URL');
        }
        
    } catch (error) {
        console.error('URL scraping failed:', error);
        addToImportHistory(`URL import failed: ${error.message}`);
        alert('Import failed: ' + error.message);
    }
}

// Trigger file import
function triggerFileImport() {
    document.getElementById('json-file-input').click();
}

// Handle file import
async function handleFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    if (!file.name.endsWith('.json')) {
        alert('Please select a JSON file');
        return;
    }
    
    try {
        addToImportHistory(`Importing file: ${file.name}`);
        
        const text = await file.text();
        const data = JSON.parse(text);
        
        // Use the existing importData function from data-manager.js
        importData(data);
        
        addToImportHistory(`File import completed: ${file.name}`);
        
        // Clear the file input
        event.target.value = '';
        
    } catch (error) {
        console.error('File import failed:', error);
        addToImportHistory(`File import failed: ${error.message}`);
        alert('File import failed: ' + error.message);
    }
}

// Add entry to import history
function addToImportHistory(message) {
    const timestamp = new Date().toLocaleString();
    const entry = `[${timestamp}] ${message}`;
    
    importHistory.unshift(entry);
    
    // Keep only last 50 entries
    if (importHistory.length > 50) {
        importHistory = importHistory.slice(0, 50);
    }
    
    updateImportHistoryDisplay();
}

// Update import history display
function updateImportHistoryDisplay() {
    const container = document.getElementById('import-history');
    if (!container) return;
    
    if (importHistory.length === 0) {
        container.innerHTML = '<p style="color: #666; font-style: italic;">No imports performed yet.</p>';
        return;
    }
    
    container.innerHTML = importHistory.map(entry => 
        `<div style="margin-bottom: 5px; padding: 5px; background-color: white; border-radius: 3px; font-size: 12px;">${entry}</div>`
    ).join('');
}

// Enhanced statistics display
function showStatistics() {
    const stats = getStatistics();
    
    const message = `
📊 NRAA Rankings Statistics

📈 Events & Shooters:
• Total Events: ${stats.totalEvents}
• Total Shooters: ${stats.totalShooters}
• Active Shooters (last 3 years): ${stats.totalActiveShooters}
• Events This Year: ${stats.eventsThisYear}

🎯 Competition Data:
• Average A Grade Count: ${stats.averageAGradeCount}
• Top Shooter: ${stats.topShooter}
• Top Shooter Points: ${stats.topShooterPoints}

📅 Import History:
• Total Imports: ${importHistory.length}
• Last Import: ${importHistory.length > 0 ? importHistory[0].split('] ')[1] : 'None'}

🏆 Recent Events:
${rankingSystem.getAllEvents().slice(0, 5).map(e => 
    `• ${e.location} ${e.date.getFullYear()} (${e.results.length} shooters)`
).join('\n')}
    `;
    
    alert(message);
}

// CORS Proxy for scraping (if needed)
function createCORSProxy(url) {
    // Note: In a real implementation, you might need a CORS proxy service
    // For now, we'll return the original URL and handle CORS issues in the scraper
    return url;
}

// Validate NRAA URL format
function validateNRAAUrl(url) {
    const patterns = [
        /results\.nraa\.com\.au\/all-kings-prize-results-\d{4}\//,
        /results\.nraa\.com\.au\/[a-z]+-kings-\d{4}-results\//,
        /results\.nraa\.com\.au\/[a-z]+-\d{4}\//
    ];
    
    return patterns.some(pattern => pattern.test(url));
}

// Quick import functions for console use
function quickImportYear(year) {
    const url = `https://www.results.nraa.com.au/all-kings-prize-results-${year}/`;
    addToImportHistory(`Quick import requested for year ${year}`);
    console.log(`Would import from: ${url}`);
    
    // For testing, add some mock data
    testScrapingFunction();
}

function quickImportState(state, year) {
    const stateCode = Object.keys(nraaScraper.stateMapping).find(
        key => nraaScraper.stateMapping[key] === state.toUpperCase()
    ) || state.toLowerCase();
    
    const url = `https://www.results.nraa.com.au/${stateCode.toLowerCase()}-kings-${year}-results/`;
    
    document.getElementById('specific-url').value = url;
    scrapeSpecificUrlFunction();
}

// Initialize import history on page load
document.addEventListener('DOMContentLoaded', function() {
    // Add import history initialization
    addToImportHistory('Import system initialized');
    
    // Add keyboard shortcuts for power users
    document.addEventListener('keydown', function(e) {
        // Ctrl+Shift+I for quick import
        if (e.ctrlKey && e.shiftKey && e.key === 'I') {
            e.preventDefault();
            switchTab('data-import');
        }
        
        // Ctrl+Shift+T for test import
        if (e.ctrlKey && e.shiftKey && e.key === 'T') {
            e.preventDefault();
            testScrapingFunction();
        }
    });
});

// Console helper functions
console.log(`
🚀 NRAA Import System Loaded!

Available functions:
• testScrapingFunction() - Test with sample data
• scrapeSpecificUrlFunction() - Import from URL in input field
• quickImportYear(year) - Quick import for a year (e.g., 2024)
• quickImportState(state, year) - Quick import for state (e.g., 'NSW', 2024)
• showStatistics() - Display system statistics

Keyboard shortcuts:
• Ctrl+Shift+I - Switch to Data Import tab
• Ctrl+Shift+T - Run test import
`);
