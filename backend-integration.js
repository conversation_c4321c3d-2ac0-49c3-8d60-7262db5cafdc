// Backend Integration Layer
// Bridges the frontend ranking system with the MySQL backend

class BackendIntegration {
    constructor() {
        this.useBackend = false;
        this.fallbackToLocal = true;
        this.syncInProgress = false;
    }

    // Initialize backend integration
    async initialize() {
        try {
            const isAvailable = await apiClient.testConnection();
            if (isAvailable) {
                this.useBackend = true;
                console.log('✅ Backend integration enabled');
                
                // Try to sync local data to backend if any exists
                await this.syncLocalDataToBackend();
                
                return true;
            } else {
                console.warn('⚠️ Backend not available, using local storage');
                this.useBackend = false;
                return false;
            }
        } catch (error) {
            console.error('Backend initialization failed:', error);
            this.useBackend = false;
            return false;
        }
    }

    // Add event (with backend sync)
    async addEvent(eventData) {
        try {
            if (this.useBackend) {
                // Format for API
                const apiEvent = apiClient.formatEventForAPI(eventData);
                const response = await apiClient.createEvent(apiEvent);
                
                console.log('✅ Event saved to backend:', response);
                
                // Also save locally as backup
                if (this.fallbackToLocal) {
                    rankingSystem.addEventLocal(eventData);
                }
                
                return response;
            } else {
                // Fallback to local storage
                return rankingSystem.addEventLocal(eventData);
            }
        } catch (error) {
            console.error('Failed to add event to backend:', error);
            
            if (this.fallbackToLocal) {
                console.log('📱 Falling back to local storage');
                return rankingSystem.addEventLocal(eventData);
            } else {
                throw error;
            }
        }
    }

    // Get all events
    async getEvents(params = {}) {
        try {
            if (this.useBackend) {
                const response = await apiClient.getEvents(params);
                return response.events.map(event => apiClient.formatEventFromAPI(event));
            } else {
                return rankingSystem.getAllEvents();
            }
        } catch (error) {
            console.error('Failed to get events from backend:', error);
            
            if (this.fallbackToLocal) {
                return rankingSystem.getAllEvents();
            } else {
                throw error;
            }
        }
    }

    // Get rankings
    async getRankings(params = {}) {
        try {
            if (this.useBackend) {
                const response = await apiClient.getRankings(params);
                return response.rankings.map(ranking => apiClient.formatRankingFromAPI(ranking));
            } else {
                return rankingSystem.getShooterRankings();
            }
        } catch (error) {
            console.error('Failed to get rankings from backend:', error);
            
            if (this.fallbackToLocal) {
                return rankingSystem.getShooterRankings();
            } else {
                throw error;
            }
        }
    }

    // Bulk import events
    async bulkImportEvents(eventsData) {
        try {
            if (this.useBackend) {
                // Format events for API
                const apiEvents = eventsData.map(event => apiClient.formatEventForAPI(event));
                const response = await apiClient.bulkImport(apiEvents);
                
                console.log('✅ Bulk import completed:', response);
                
                // Also save locally as backup
                if (this.fallbackToLocal) {
                    eventsData.forEach(event => rankingSystem.addEventLocal(event));
                }
                
                return response;
            } else {
                // Fallback to local import
                const results = {
                    imported: 0,
                    skipped: 0,
                    errors: 0,
                    details: []
                };
                
                eventsData.forEach(event => {
                    try {
                        const errors = rankingSystem.validateEventData(event);
                        if (errors.length === 0) {
                            rankingSystem.addEventLocal(event);
                            results.imported++;
                            results.details.push(`Imported: ${event.location} ${event.date}`);
                        } else {
                            results.errors++;
                            results.details.push(`Error: ${event.location} ${event.date} - ${errors.join(', ')}`);
                        }
                    } catch (error) {
                        results.errors++;
                        results.details.push(`Error: ${event.location} ${event.date} - ${error.message}`);
                    }
                });
                
                return { results };
            }
        } catch (error) {
            console.error('Bulk import failed:', error);
            throw error;
        }
    }

    // Sync local data to backend
    async syncLocalDataToBackend() {
        if (this.syncInProgress || !this.useBackend) return;
        
        try {
            this.syncInProgress = true;
            console.log('🔄 Syncing local data to backend...');
            
            const localEvents = rankingSystem.getAllEvents();
            if (localEvents.length === 0) {
                console.log('📭 No local events to sync');
                return;
            }
            
            // Validate before syncing
            const response = await apiClient.validateImport(
                localEvents.map(event => apiClient.formatEventForAPI(event))
            );
            
            if (response.results.valid > 0) {
                const validEvents = localEvents.filter((event, index) => {
                    // This is a simplified check - in reality you'd need more sophisticated validation
                    return !response.results.details[index]?.includes('Duplicate');
                });
                
                if (validEvents.length > 0) {
                    await this.bulkImportEvents(validEvents);
                    console.log(`✅ Synced ${validEvents.length} local events to backend`);
                }
            }
            
        } catch (error) {
            console.error('Sync failed:', error);
        } finally {
            this.syncInProgress = false;
        }
    }

    // Get backend statistics
    async getStatistics() {
        try {
            if (this.useBackend) {
                return await apiClient.getRankingStats();
            } else {
                return getStatistics(); // Use local statistics function
            }
        } catch (error) {
            console.error('Failed to get statistics from backend:', error);
            return getStatistics(); // Fallback to local
        }
    }

    // Delete event
    async deleteEvent(eventId) {
        try {
            if (this.useBackend && eventId) {
                await apiClient.deleteEvent(eventId);
                console.log('✅ Event deleted from backend');
            }
            
            // Also remove from local storage
            // Note: This would need to be implemented in the ranking system
            console.log('📱 Event removed from local storage');
            
        } catch (error) {
            console.error('Failed to delete event:', error);
            throw error;
        }
    }

    // Get shooter autocomplete suggestions
    async getShooterSuggestions(query) {
        try {
            if (this.useBackend && query.length >= 2) {
                return await apiClient.getShooterAutocomplete(query);
            } else {
                // Fallback to local shooter names
                const allShooters = new Set();
                rankingSystem.getAllEvents().forEach(event => {
                    event.results.forEach(result => {
                        if (result.shooter.toLowerCase().includes(query.toLowerCase())) {
                            allShooters.add(result.shooter);
                        }
                    });
                });
                return Array.from(allShooters).slice(0, 20);
            }
        } catch (error) {
            console.error('Failed to get shooter suggestions:', error);
            return [];
        }
    }

    // Check if backend is available
    isBackendAvailable() {
        return this.useBackend && apiClient.isBackendAvailable();
    }

    // Force refresh from backend
    async refreshFromBackend() {
        if (!this.useBackend) return false;
        
        try {
            console.log('🔄 Refreshing data from backend...');
            
            // Clear local data
            rankingSystem.events = [];
            
            // Load from backend
            const events = await this.getEvents();
            events.forEach(event => {
                rankingSystem.events.push(event);
            });
            
            // Update rankings
            rankingSystem.updateShooterRankings();
            
            console.log(`✅ Refreshed ${events.length} events from backend`);
            return true;
            
        } catch (error) {
            console.error('Failed to refresh from backend:', error);
            return false;
        }
    }

    // Export data (works with both backend and local)
    async exportData() {
        try {
            const events = await this.getEvents();
            const rankings = await this.getRankings();
            const stats = await this.getStatistics();
            
            return {
                events,
                rankings,
                statistics: stats,
                exportDate: new Date().toISOString(),
                source: this.useBackend ? 'backend' : 'local'
            };
        } catch (error) {
            console.error('Export failed:', error);
            throw error;
        }
    }
}

// Global backend integration instance
const backendIntegration = new BackendIntegration();

// Initialize on page load
document.addEventListener('DOMContentLoaded', async () => {
    await backendIntegration.initialize();
});

// Helper functions for UI
async function addEventWithBackend(eventData) {
    return await backendIntegration.addEvent(eventData);
}

async function getEventsWithBackend(params = {}) {
    return await backendIntegration.getEvents(params);
}

async function getRankingsWithBackend(params = {}) {
    return await backendIntegration.getRankings(params);
}

async function bulkImportWithBackend(eventsData) {
    return await backendIntegration.bulkImportEvents(eventsData);
}

async function refreshDataFromBackend() {
    return await backendIntegration.refreshFromBackend();
}
