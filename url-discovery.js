// URL Discovery Tool for NRAA Results
// Discovers actual competition URLs from year pages

class URLDiscovery {
    constructor() {
        this.scraper = new NRAAScraper();
        this.discoveredUrls = {};
    }

    // Discover URLs using a CORS proxy or manual method
    async discoverUrlsWithProxy(year) {
        const yearUrl = this.scraper.yearUrls[year];
        if (!yearUrl) {
            throw new Error(`No URL configured for year ${year}`);
        }

        const fullUrl = this.scraper.baseUrl + yearUrl;
        
        try {
            // Try to use a public CORS proxy
            const proxyUrls = [
                `https://api.allorigins.win/get?url=${encodeURIComponent(fullUrl)}`,
                `https://corsproxy.io/?${encodeURIComponent(fullUrl)}`,
                `https://cors-anywhere.herokuapp.com/${fullUrl}`
            ];

            for (const proxyUrl of proxyUrls) {
                try {
                    console.log(`Trying proxy: ${proxyUrl}`);
                    const response = await fetch(proxyUrl);
                    
                    if (response.ok) {
                        let html;
                        const data = await response.json();
                        
                        // Handle different proxy response formats
                        if (data.contents) {
                            html = data.contents; // allorigins format
                        } else if (data.data) {
                            html = data.data; // other proxy format
                        } else {
                            html = await response.text(); // direct response
                        }

                        const parser = new DOMParser();
                        const doc = parser.parseFromString(html, 'text/html');
                        
                        const links = this.scraper.extractStateLinks(doc, year);
                        this.discoveredUrls[year] = links;
                        
                        return links;
                    }
                } catch (proxyError) {
                    console.warn(`Proxy failed: ${proxyUrl}`, proxyError);
                    continue;
                }
            }
            
            throw new Error('All CORS proxies failed');
            
        } catch (error) {
            console.error(`URL discovery failed for ${year}:`, error);
            throw error;
        }
    }

    // Manual URL discovery - provides instructions and tools
    showManualUrlDiscovery(year) {
        const yearUrl = this.scraper.baseUrl + this.scraper.yearUrls[year];
        
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content" style="max-width: 700px;">
                <h3>🔍 Manual URL Discovery for ${year}</h3>
                
                <div style="margin-bottom: 20px; padding: 15px; background-color: #e3f2fd; border-radius: 4px;">
                    <strong>Step 1:</strong> Visit the ${year} results page:
                    <div style="margin: 10px 0;">
                        <a href="${yearUrl}" target="_blank" style="color: #1976d2; text-decoration: none; font-weight: bold;">
                            📄 ${yearUrl}
                        </a>
                        <button onclick="navigator.clipboard.writeText('${yearUrl}'); alert('URL copied!')" 
                                style="margin-left: 10px; padding: 5px 10px; background-color: #1976d2; color: white; border: none; border-radius: 3px; cursor: pointer;">
                            Copy URL
                        </button>
                    </div>
                </div>

                <div style="margin-bottom: 20px; padding: 15px; background-color: #f3e5f5; border-radius: 4px;">
                    <strong>Step 2:</strong> Copy the page source:
                    <ol style="margin: 10px 0; padding-left: 20px;">
                        <li>Right-click on the page and select "View Page Source" (or press Ctrl+U)</li>
                        <li>Select all the HTML source code (Ctrl+A)</li>
                        <li>Copy it (Ctrl+C)</li>
                        <li>Paste it in the text area below</li>
                    </ol>
                </div>

                <div style="margin-bottom: 15px;">
                    <label><strong>Step 3:</strong> Paste the HTML source here:</label>
                    <textarea id="html-source-${year}" placeholder="Paste the HTML source code from the ${year} results page here..." 
                              style="width: 100%; height: 200px; font-family: monospace; font-size: 11px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; margin-top: 5px;"></textarea>
                </div>

                <div style="margin-bottom: 15px;">
                    <button onclick="urlDiscovery.parseManualHtml(${year})" 
                            style="padding: 10px 20px; background-color: #4caf50; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                        🔍 Discover URLs
                    </button>
                    <button onclick="urlDiscovery.loadSampleHtml(${year})" 
                            style="padding: 10px 20px; background-color: #ff9800; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        📝 Load Sample HTML
                    </button>
                </div>

                <div id="discovered-urls-${year}" style="margin-top: 15px; padding: 10px; background-color: #f5f5f5; border-radius: 4px; min-height: 50px;">
                    <em>Discovered URLs will appear here...</em>
                </div>

                <div style="margin-top: 20px; display: flex; gap: 10px;">
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" 
                            style="flex: 1; padding: 10px; background-color: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        Close
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    // Parse manually provided HTML
    parseManualHtml(year) {
        const htmlSource = document.getElementById(`html-source-${year}`).value;
        const resultsDiv = document.getElementById(`discovered-urls-${year}`);
        
        if (!htmlSource.trim()) {
            resultsDiv.innerHTML = '<span style="color: #f44336;">Please paste the HTML source first.</span>';
            return;
        }

        try {
            const parser = new DOMParser();
            const doc = parser.parseFromString(htmlSource, 'text/html');
            
            const links = this.scraper.extractStateLinks(doc, year);
            this.discoveredUrls[year] = links;
            
            if (links.length === 0) {
                resultsDiv.innerHTML = '<span style="color: #ff9800;">No competition links found. Please check the HTML source.</span>';
                return;
            }

            let html = `<strong>🎉 Found ${links.length} competition URLs:</strong><br><br>`;
            
            links.forEach((link, index) => {
                html += `
                    <div style="margin-bottom: 10px; padding: 10px; background-color: white; border-radius: 4px; border-left: 4px solid #4caf50;">
                        <strong>${link.state} (${link.stateCode})</strong><br>
                        <small style="color: #666;">${link.text}</small><br>
                        <a href="${link.url}" target="_blank" style="color: #1976d2; font-size: 12px; word-break: break-all;">${link.url}</a>
                        <button onclick="urlDiscovery.importSingleUrl('${link.url}', '${link.state}', '${year}')" 
                                style="margin-left: 10px; padding: 3px 8px; background-color: #4caf50; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 11px;">
                            Import
                        </button>
                    </div>
                `;
            });

            html += `
                <div style="margin-top: 15px; text-align: center;">
                    <button onclick="urlDiscovery.importAllDiscoveredUrls(${year})" 
                            style="padding: 10px 20px; background-color: #2196f3; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        📥 Import All ${links.length} Competitions
                    </button>
                </div>
            `;

            resultsDiv.innerHTML = html;

        } catch (error) {
            console.error('HTML parsing failed:', error);
            resultsDiv.innerHTML = `<span style="color: #f44336;">Error parsing HTML: ${error.message}</span>`;
        }
    }

    // Load sample HTML for testing
    loadSampleHtml(year) {
        const sampleHtml = `
<!DOCTYPE html>
<html>
<head><title>All King's Prize Results - ${year}</title></head>
<body>
    <h1>All King's Prize Results - ${year}</h1>
    <div class="competition-links">
        <a href="/act-${year}/">ACT ${year}</a>
        <a href="/nqra-kings-${year}-results/">NQRA ${year}</a>
        <a href="/nswra-kings-${year}/">NSWRA ${year}</a>
        <a href="/sara-kings-prize-${year}/">SARA ${year}</a>
        <a href="/vra-${year}-kings/">VRA ${year}</a>
        <a href="/wara-kings-${year}-results/">WARA ${year}</a>
    </div>
</body>
</html>
        `;
        
        document.getElementById(`html-source-${year}`).value = sampleHtml;
        alert('Sample HTML loaded! Click "🔍 Discover URLs" to test the parser.');
    }

    // Import a single discovered URL
    async importSingleUrl(url, state, year) {
        try {
            addToImportHistory(`Importing single URL: ${state} ${year}`);
            
            const stateLink = {
                state: state,
                stateCode: state,
                year: year,
                url: url,
                text: `${state} ${year}`
            };

            // For now, we'll use the manual import method since CORS is still an issue
            document.getElementById('specific-url').value = url;
            await scrapeSpecificUrlFunction();
            
        } catch (error) {
            console.error('Single URL import failed:', error);
            addToImportHistory(`Single URL import failed: ${error.message}`);
            alert('Import failed: ' + error.message);
        }
    }

    // Import all discovered URLs for a year
    async importAllDiscoveredUrls(year) {
        const links = this.discoveredUrls[year];
        if (!links || links.length === 0) {
            alert('No URLs discovered for this year');
            return;
        }

        const confirmMsg = `Import all ${links.length} competitions for ${year}?\n\n${links.map(l => `• ${l.state}`).join('\n')}`;
        
        if (!confirm(confirmMsg)) {
            return;
        }

        addToImportHistory(`Starting bulk import: ${links.length} competitions for ${year}`);

        let imported = 0;
        let failed = 0;

        for (const link of links) {
            try {
                await this.importSingleUrl(link.url, link.state, year);
                imported++;
                
                // Add a small delay between imports
                await new Promise(resolve => setTimeout(resolve, 1000));
                
            } catch (error) {
                console.error(`Failed to import ${link.state}:`, error);
                failed++;
            }
        }

        const summary = `Bulk import completed!\n\nImported: ${imported}\nFailed: ${failed}`;
        addToImportHistory(summary);
        alert(summary);
    }

    // Get all discovered URLs
    getAllDiscoveredUrls() {
        return this.discoveredUrls;
    }

    // Export discovered URLs as JSON
    exportDiscoveredUrls() {
        const data = {
            discoveredUrls: this.discoveredUrls,
            exportDate: new Date().toISOString(),
            version: '1.0'
        };
        
        const jsonString = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `nraa_discovered_urls_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// Global instance
const urlDiscovery = new URLDiscovery();

// Helper functions
function discoverUrlsForYear(year) {
    urlDiscovery.showManualUrlDiscovery(year);
}

function showAllYearDiscovery() {
    const years = Object.keys(urlDiscovery.scraper.yearUrls);
    const message = `🔍 URL Discovery Available for:\n\n${years.map(year => `• ${year}`).join('\n')}\n\nWhich year would you like to discover URLs for?`;
    
    const year = prompt(message + '\n\nEnter year (2023, 2024, or 2025):');
    if (year && urlDiscovery.scraper.yearUrls[year]) {
        discoverUrlsForYear(year);
    } else if (year) {
        alert('Invalid year. Please enter 2023, 2024, or 2025.');
    }
}
