// Example of how to add event data
function addKingsPrizeEvent() {
    const eventData = {
        date: '2024-01-15',
        location: 'NSW',
        aGradeCount: 45,
        results: [
            { shooter: '<PERSON>', position: 1 },
            { shooter: '<PERSON>', position: 2 },
            // ... more results
        ]
    };
    
    rankingSystem.addEvent(eventData);
    displayRankings();
}

// Load sample data for testing (disabled by default)
function loadSampleData() {
    // Sample data loading is disabled for clean start
    // Uncomment the code below if you want to load sample data for testing

    /*
    const sampleEvents = [
        {
            date: '2024-01-15',
            location: 'NSW',
            aGradeCount: 45,
            results: [
                { shooter: '<PERSON>', position: 1 },
                { shooter: '<PERSON>', position: 2 },
                { shooter: '<PERSON>', position: 3 },
                { shooter: '<PERSON>', position: 4 },
                { shooter: '<PERSON>', position: 5 }
            ]
        }
        // Add more sample events here if needed
    ];

    sampleEvents.forEach(event => rankingSystem.addEvent(event));
    displayRankings();
    */

    console.log('Starting with clean data - no sample events loaded');
    displayRankings(); // Display empty rankings table
}

// Quick add functions for testing score entry
function quickAddEvent(location, aGradeCount, results) {
    const eventData = {
        date: new Date().toISOString().split('T')[0], // Today's date
        location: location,
        aGradeCount: aGradeCount,
        results: results
    };

    rankingSystem.addEvent(eventData);
    displayRankings();
    displayEvents();

    console.log(`Added event: ${location} with ${results.length} shooters`);
}

// Export data to JSON (for backup/sharing)
function exportData() {
    const data = {
        events: rankingSystem.events,
        exportDate: new Date().toISOString(),
        version: '1.0'
    };

    const jsonString = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `nraa_rankings_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    console.log('Data exported successfully');
}

// Import data from JSON
function importData(jsonData) {
    try {
        const data = typeof jsonData === 'string' ? JSON.parse(jsonData) : jsonData;

        if (!data.events || !Array.isArray(data.events)) {
            throw new Error('Invalid data format');
        }

        // Clear existing data
        rankingSystem.events = [];

        // Import events
        data.events.forEach(eventData => {
            // Convert date string back to Date object
            eventData.date = new Date(eventData.date);
            rankingSystem.events.push(eventData);
        });

        // Update rankings
        rankingSystem.updateShooterRankings();
        displayRankings();
        displayEvents();

        console.log(`Imported ${data.events.length} events successfully`);

        // Add to import history if function exists
        if (typeof addToImportHistory === 'function') {
            addToImportHistory(`Manual JSON import: ${data.events.length} events imported`);
        }

        alert(`Successfully imported ${data.events.length} events`);

    } catch (error) {
        console.error('Import failed:', error);

        // Add to import history if function exists
        if (typeof addToImportHistory === 'function') {
            addToImportHistory(`Manual JSON import failed: ${error.message}`);
        }

        alert('Failed to import data. Please check the file format.');
    }
}

// Clear all data (with confirmation)
function clearAllData() {
    if (confirm('Are you sure you want to delete ALL events and rankings? This cannot be undone!')) {
        if (confirm('This will permanently delete all data. Are you absolutely sure?')) {
            rankingSystem.events = [];
            rankingSystem.shooters.clear();
            displayRankings();
            displayEvents();
            scoreEntry.clearResults();

            console.log('All data cleared');
            alert('All data has been cleared');
        }
    }
}

// Get statistics
function getStatistics() {
    const events = rankingSystem.getAllEvents();
    const shooters = rankingSystem.getAllShooterNames();
    const rankings = rankingSystem.getRankings();

    const stats = {
        totalEvents: events.length,
        totalShooters: shooters.length,
        totalActiveShooters: rankings.length,
        eventsThisYear: events.filter(e => e.date.getFullYear() === new Date().getFullYear()).length,
        averageAGradeCount: events.length > 0 ? Math.round(events.reduce((sum, e) => sum + e.aGradeCount, 0) / events.length) : 0,
        topShooter: rankings.length > 0 ? rankings[0].name : 'None',
        topShooterPoints: rankings.length > 0 ? rankings[0].totalPoints : 0
    };

    console.log('Statistics:', stats);
    return stats;
}

// Display statistics in console
function showStatistics() {
    const stats = getStatistics();
    console.log(`
=== NRAA Rankings Statistics ===
Total Events: ${stats.totalEvents}
Total Shooters: ${stats.totalShooters}
Active Shooters (last 3 years): ${stats.totalActiveShooters}
Events This Year: ${stats.eventsThisYear}
Average A Grade Count: ${stats.averageAGradeCount}
Top Shooter: ${stats.topShooter} (${stats.topShooterPoints} points)
================================
    `);
}
