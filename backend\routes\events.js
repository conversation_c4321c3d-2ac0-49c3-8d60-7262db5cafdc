const express = require('express');
const Joi = require('joi');
const { executeQuery, getConnection, normalizeShooterName, calculatePoints } = require('../config/database');

const router = express.Router();

// Validation schemas
const eventSchema = Joi.object({
  date: Joi.date().required(),
  location: Joi.string().max(50).required(),
  aGradeCount: Joi.number().integer().min(1).required(),
  competitionName: Joi.string().max(255).optional(),
  sourceUrl: Joi.string().uri().optional(),
  results: Joi.array().items(
    Joi.object({
      shooter: Joi.string().max(255).required(),
      position: Joi.number().integer().min(1).required(),
      score: Joi.number().optional()
    })
  ).min(1).required()
});

// GET /api/events - Get all events
router.get('/', async (req, res) => {
  try {
    const { limit = 50, offset = 0, location, year } = req.query;
    
    let query = `
      SELECT 
        e.*,
        COUNT(r.id) as total_shooters
      FROM events e
      LEFT JOIN results r ON e.id = r.event_id
    `;
    
    const conditions = [];
    const params = [];
    
    if (location) {
      conditions.push('e.location = ?');
      params.push(location);
    }
    
    if (year) {
      conditions.push('YEAR(e.date) = ?');
      params.push(year);
    }
    
    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }
    
    query += `
      GROUP BY e.id
      ORDER BY e.date DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(parseInt(limit), parseInt(offset));
    
    const events = await executeQuery(query, params);
    
    res.json({
      events,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: events.length
      }
    });
  } catch (error) {
    console.error('Error fetching events:', error);
    res.status(500).json({ error: 'Failed to fetch events' });
  }
});

// GET /api/events/:id - Get specific event with results
router.get('/:id', async (req, res) => {
  try {
    const eventId = parseInt(req.params.id);
    
    // Get event details
    const events = await executeQuery(
      'SELECT * FROM events WHERE id = ?',
      [eventId]
    );
    
    if (events.length === 0) {
      return res.status(404).json({ error: 'Event not found' });
    }
    
    const event = events[0];
    
    // Get event results
    const results = await executeQuery(`
      SELECT 
        r.position,
        r.score,
        r.points,
        s.name as shooter_name
      FROM results r
      JOIN shooters s ON r.shooter_id = s.id
      WHERE r.event_id = ?
      ORDER BY r.position ASC
    `, [eventId]);
    
    event.results = results;
    
    res.json(event);
  } catch (error) {
    console.error('Error fetching event:', error);
    res.status(500).json({ error: 'Failed to fetch event' });
  }
});

// POST /api/events - Create new event
router.post('/', async (req, res) => {
  const connection = await getConnection();
  
  try {
    // Validate request body
    const { error, value } = eventSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        details: error.details.map(d => d.message)
      });
    }
    
    const { date, location, aGradeCount, competitionName, sourceUrl, results } = value;
    
    await connection.beginTransaction();
    
    // Check for duplicate event
    const existingEvents = await connection.execute(
      'SELECT id FROM events WHERE date = ? AND location = ? AND a_grade_count = ?',
      [date, location, aGradeCount]
    );
    
    if (existingEvents[0].length > 0) {
      await connection.rollback();
      return res.status(409).json({ 
        error: 'Event already exists',
        existingEventId: existingEvents[0][0].id
      });
    }
    
    // Create event
    const [eventResult] = await connection.execute(`
      INSERT INTO events (date, location, a_grade_count, competition_name, source_url)
      VALUES (?, ?, ?, ?, ?)
    `, [date, location, aGradeCount, competitionName, sourceUrl]);
    
    const eventId = eventResult.insertId;
    
    // Process shooters and results
    for (const result of results) {
      // Get or create shooter
      const normalizedName = normalizeShooterName(result.shooter);
      
      let shooterId;
      const existingShooters = await connection.execute(
        'SELECT id FROM shooters WHERE normalized_name = ?',
        [normalizedName]
      );
      
      if (existingShooters[0].length > 0) {
        shooterId = existingShooters[0][0].id;
      } else {
        const [shooterResult] = await connection.execute(
          'INSERT INTO shooters (name, normalized_name) VALUES (?, ?)',
          [result.shooter, normalizedName]
        );
        shooterId = shooterResult.insertId;
      }
      
      // Calculate points
      const points = calculatePoints(result.position, aGradeCount);
      
      // Insert result
      await connection.execute(`
        INSERT INTO results (event_id, shooter_id, position, score, points)
        VALUES (?, ?, ?, ?, ?)
      `, [eventId, shooterId, result.position, result.score || null, points]);
    }
    
    await connection.commit();
    
    res.status(201).json({
      message: 'Event created successfully',
      eventId,
      shootersProcessed: results.length
    });
    
  } catch (error) {
    await connection.rollback();
    console.error('Error creating event:', error);
    
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(409).json({ 
        error: 'Duplicate entry detected',
        details: error.message
      });
    }
    
    res.status(500).json({ error: 'Failed to create event' });
  } finally {
    connection.release();
  }
});

// PUT /api/events/:id - Update event
router.put('/:id', async (req, res) => {
  const connection = await getConnection();
  
  try {
    const eventId = parseInt(req.params.id);
    
    // Validate request body
    const { error, value } = eventSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ 
        error: 'Validation failed', 
        details: error.details.map(d => d.message)
      });
    }
    
    const { date, location, aGradeCount, competitionName, sourceUrl, results } = value;
    
    await connection.beginTransaction();
    
    // Check if event exists
    const existingEvents = await connection.execute(
      'SELECT id FROM events WHERE id = ?',
      [eventId]
    );
    
    if (existingEvents[0].length === 0) {
      await connection.rollback();
      return res.status(404).json({ error: 'Event not found' });
    }
    
    // Update event
    await connection.execute(`
      UPDATE events 
      SET date = ?, location = ?, a_grade_count = ?, competition_name = ?, source_url = ?
      WHERE id = ?
    `, [date, location, aGradeCount, competitionName, sourceUrl, eventId]);
    
    // Delete existing results
    await connection.execute('DELETE FROM results WHERE event_id = ?', [eventId]);
    
    // Process new results (same logic as POST)
    for (const result of results) {
      const normalizedName = normalizeShooterName(result.shooter);
      
      let shooterId;
      const existingShooters = await connection.execute(
        'SELECT id FROM shooters WHERE normalized_name = ?',
        [normalizedName]
      );
      
      if (existingShooters[0].length > 0) {
        shooterId = existingShooters[0][0].id;
      } else {
        const [shooterResult] = await connection.execute(
          'INSERT INTO shooters (name, normalized_name) VALUES (?, ?)',
          [result.shooter, normalizedName]
        );
        shooterId = shooterResult.insertId;
      }
      
      const points = calculatePoints(result.position, aGradeCount);
      
      await connection.execute(`
        INSERT INTO results (event_id, shooter_id, position, score, points)
        VALUES (?, ?, ?, ?, ?)
      `, [eventId, shooterId, result.position, result.score || null, points]);
    }
    
    await connection.commit();
    
    res.json({
      message: 'Event updated successfully',
      eventId,
      shootersProcessed: results.length
    });
    
  } catch (error) {
    await connection.rollback();
    console.error('Error updating event:', error);
    res.status(500).json({ error: 'Failed to update event' });
  } finally {
    connection.release();
  }
});

// DELETE /api/events/:id - Delete event
router.delete('/:id', async (req, res) => {
  try {
    const eventId = parseInt(req.params.id);
    
    const result = await executeQuery(
      'DELETE FROM events WHERE id = ?',
      [eventId]
    );
    
    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Event not found' });
    }
    
    res.json({ message: 'Event deleted successfully' });
  } catch (error) {
    console.error('Error deleting event:', error);
    res.status(500).json({ error: 'Failed to delete event' });
  }
});

module.exports = router;
