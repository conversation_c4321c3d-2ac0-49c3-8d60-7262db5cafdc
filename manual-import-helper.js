// Manual Import Helper for NRAA Results
// Provides tools for manual data entry when auto-scraping is blocked by CORS

class ManualImportHelper {
    constructor() {
        this.nraaUrls = {
            2025: 'https://www.results.nraa.com.au/all-kings-prize-results-2025/',
            2024: 'https://www.results.nraa.com.au/all-kings-prize-results-2024/',
            2023: 'https://www.results.nraa.com.au/all-kings-prize-results-2023/'
        };
        
        this.statePatterns = {
            'ACT': ['act', 'canberra'],
            'NSW': ['nsw', 'nswra', 'new south wales'],
            'NT': ['nt', 'ntra', 'northern territory'],
            'QLD': ['qld', 'nqra', 'queensland'],
            'SA': ['sa', 'sara', 'south australia'],
            'TAS': ['tas', 'tra', 'tasmania'],
            'VIC': ['vic', 'vra', 'victoria'],
            'WA': ['wa', 'wara', 'western australia']
        };
    }

    // Show manual import instructions
    showManualImportInstructions() {
        const instructions = `
🔧 Manual Import Instructions

Due to browser security restrictions (CORS), automatic scraping is not possible directly from the browser. Here are your options:

📋 OPTION 1: Manual Copy-Paste
1. Visit the NRAA results pages manually
2. Copy the Kings Aggregate results table
3. Use the paste import function below

🔍 OPTION 2: URL Discovery
1. Use "🔍 Discover URLs" to find actual competition links
2. Copy HTML source from year pages
3. System extracts real URLs (not assumed patterns)
4. Import individual competitions or all at once

📂 OPTION 3: Download & Import
1. Save the webpage as HTML
2. Use the file import function
3. The system will extract the data

🌐 OPTION 4: Browser Extension
1. Install a CORS-disabling browser extension
2. Temporarily disable CORS for results.nraa.com.au
3. Use the auto-scraper

⚙️ OPTION 5: Server-Side Scraping
1. Run this application on a server
2. Server environments don't have CORS restrictions
3. Auto-scraper will work normally

Click "Show URLs" to get direct links to all competition pages.
        `;
        
        alert(instructions);
    }

    // Generate and display all NRAA URLs
    showAllUrls() {
        let urlList = '🔗 NRAA Competition URLs\n\n';
        
        Object.entries(this.nraaUrls).forEach(([year, yearUrl]) => {
            urlList += `📅 ${year} Results:\n${yearUrl}\n\n`;
            
            // Add common state URLs for each year
            Object.keys(this.statePatterns).forEach(state => {
                const stateCode = this.statePatterns[state][0];
                const stateUrl = `https://www.results.nraa.com.au/${stateCode}-kings-${year}-results/`;
                urlList += `  • ${state}: ${stateUrl}\n`;
            });
            urlList += '\n';
        });
        
        urlList += '\n💡 Tip: Right-click and "Open in new tab" to visit these URLs';
        
        // Create a modal with copyable URLs
        this.showUrlModal(urlList);
    }

    // Create a modal with copyable URLs
    showUrlModal(content) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content" style="max-width: 600px;">
                <h3>NRAA Competition URLs</h3>
                <textarea readonly style="width: 100%; height: 400px; font-family: monospace; font-size: 12px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">${content}</textarea>
                <div style="margin-top: 15px; display: flex; gap: 10px;">
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" style="flex: 1; padding: 10px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">Close</button>
                    <button onclick="navigator.clipboard.writeText(this.parentElement.previousElementSibling.value); alert('URLs copied to clipboard!')" style="flex: 1; padding: 10px; background-color: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">Copy All URLs</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    // Parse pasted table data (handles both tab-separated and HTML)
    parseTableData(pastedText, eventInfo = {}) {
        // Check if this looks like HTML content
        if (pastedText.includes('<') && pastedText.includes('>')) {
            return this.parseHtmlTableData(pastedText, eventInfo);
        }

        // Parse as tab-separated text
        const lines = pastedText.split('\n').map(line => line.trim()).filter(line => line);
        const results = [];

        let headerFound = false;
        let positionCol = -1;
        let nameCol = -1;
        let firstNameCol = -1;
        let lastNameCol = -1;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const cells = line.split('\t').map(cell => cell.trim());
            
            // Skip empty lines
            if (cells.length < 2) continue;
            
            // Look for header row
            if (!headerFound) {
                const headerText = line.toLowerCase();
                if (headerText.includes('place') || headerText.includes('pos')) {
                    headerFound = true;
                    
                    // Map columns
                    cells.forEach((cell, index) => {
                        const cellText = cell.toLowerCase();
                        if (cellText.includes('place') || cellText.includes('pos')) {
                            positionCol = index;
                        } else if (cellText.includes('last') && cellText.includes('name')) {
                            lastNameCol = index;
                        } else if (cellText.includes('first') || cellText.includes('preferred')) {
                            firstNameCol = index;
                        } else if (cellText.includes('name') && nameCol === -1) {
                            nameCol = index;
                        }
                    });
                    continue;
                }
            }
            
            // Parse data rows
            if (headerFound && cells.length > Math.max(positionCol, nameCol, lastNameCol, firstNameCol)) {
                const position = parseInt(cells[positionCol]);
                
                if (isNaN(position) || position < 1) continue;
                
                let shooterName = '';
                if (nameCol >= 0 && cells[nameCol]) {
                    shooterName = cells[nameCol];
                } else if (firstNameCol >= 0 && lastNameCol >= 0) {
                    const firstName = cells[firstNameCol] || '';
                    const lastName = cells[lastNameCol] || '';
                    shooterName = `${firstName} ${lastName}`.trim();
                }
                
                if (shooterName) {
                    results.push({
                        shooter: shooterName,
                        position: position
                    });
                }
            }
        }
        
        // Create event data
        if (results.length > 0) {
            const eventData = {
                date: eventInfo.date || new Date().toISOString().split('T')[0],
                location: eventInfo.location || 'Unknown',
                aGradeCount: results.length,
                results: results.sort((a, b) => a.position - b.position)
            };
            
            return eventData;
        }
        
        return null;
    }

    // Parse HTML table data (for when users paste HTML source)
    parseHtmlTableData(htmlText, eventInfo = {}) {
        try {
            const parser = new DOMParser();
            const doc = parser.parseFromString(htmlText, 'text/html');

            // Use the same logic as the scraper to find Kings Aggregate sections
            const kingsSection = this.findKingsAggregateInHtml(doc);
            if (!kingsSection) {
                throw new Error('No Kings Aggregate section found in the HTML');
            }

            // Extract results from the table
            const results = this.extractResultsFromHtmlTable(kingsSection);

            if (results.length === 0) {
                throw new Error('No results found in the Kings Aggregate table');
            }

            // Create event data
            const eventData = {
                date: eventInfo.date || new Date().toISOString().split('T')[0],
                location: eventInfo.location || 'Unknown',
                aGradeCount: results.length,
                results: results.sort((a, b) => a.position - b.position)
            };

            return eventData;

        } catch (error) {
            console.error('HTML parsing failed:', error);
            throw new Error(`HTML parsing failed: ${error.message}`);
        }
    }

    // Find Kings Aggregate section in HTML (similar to scraper logic)
    findKingsAggregateInHtml(doc) {
        console.log('Looking for Kings Aggregate in HTML...');

        // Look for collapsible sections first
        const collapsibleSections = doc.querySelectorAll('.collapsible, div[class*="collaps"], div[style*="collaps"]');

        for (const section of collapsibleSections) {
            const headings = section.querySelectorAll('h1, h2, h3, h4, h5, h6');

            for (const heading of headings) {
                const text = heading.textContent.toLowerCase();

                if (text.includes('kings') &&
                    text.includes('aggregate') &&
                    !text.includes('grand') &&
                    !text.includes('day 1') &&
                    !text.includes('day 2') &&
                    !text.includes('day 3')) {

                    console.log('Found Kings Aggregate section:', text);

                    // Look for table in this section or nearby
                    let table = section.querySelector('table');
                    if (table) return table;

                    // Check next siblings
                    let nextSibling = section.nextElementSibling;
                    while (nextSibling) {
                        table = nextSibling.querySelector('table');
                        if (table) return table;

                        if (nextSibling.classList.contains('collapsible')) break;
                        nextSibling = nextSibling.nextElementSibling;
                    }
                }
            }
        }

        // Fallback: look for any heading with Kings Aggregate
        const allHeadings = doc.querySelectorAll('h1, h2, h3, h4, h5, h6');
        for (const heading of allHeadings) {
            const text = heading.textContent.toLowerCase();
            if (text.includes('kings') && text.includes('aggregate') && !text.includes('grand')) {
                let current = heading.nextElementSibling;
                while (current) {
                    if (current.tagName === 'TABLE') return current;
                    const table = current.querySelector('table');
                    if (table) return table;
                    current = current.nextElementSibling;
                }
            }
        }

        return null;
    }

    // Extract results from HTML table
    extractResultsFromHtmlTable(table) {
        const results = [];
        const rows = table.querySelectorAll('tr');

        // Find header row
        let headerRow = null;
        let dataStartIndex = 0;

        for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            const cells = row.querySelectorAll('th, td');
            const cellTexts = Array.from(cells).map(c => c.textContent.toLowerCase().trim());

            if (cellTexts.some(text => text.includes('place') || text.includes('pos')) &&
                cellTexts.some(text => text.includes('name'))) {
                headerRow = row;
                dataStartIndex = i + 1;
                break;
            }
        }

        if (!headerRow) {
            console.warn('No header row found in table');
            return results;
        }

        // Map column indices
        const columnMap = this.mapHtmlTableColumns(headerRow);

        // Extract data rows
        for (let i = dataStartIndex; i < rows.length; i++) {
            const row = rows[i];
            const cells = row.querySelectorAll('td');

            if (cells.length < 2) continue;

            const result = this.extractHtmlRowData(cells, columnMap);
            if (result) {
                results.push(result);
            }
        }

        return results;
    }

    // Map HTML table columns
    mapHtmlTableColumns(headerRow) {
        const headers = headerRow.querySelectorAll('th, td');
        const columnMap = {};

        headers.forEach((header, index) => {
            const text = header.textContent.toLowerCase().trim();

            if (text.includes('place') || text.includes('pos')) {
                columnMap.position = index;
            } else if (text.includes('last') && text.includes('name')) {
                columnMap.lastName = index;
            } else if (text.includes('first') || text.includes('preferred')) {
                columnMap.firstName = index;
            } else if (text.includes('name') && !columnMap.lastName) {
                columnMap.fullName = index;
            }
        });

        return columnMap;
    }

    // Extract data from HTML table row
    extractHtmlRowData(cells, columnMap) {
        try {
            const positionText = cells[columnMap.position]?.textContent.trim();
            const position = parseInt(positionText);

            if (isNaN(position) || position < 1) return null;

            let shooterName = '';
            if (columnMap.fullName !== undefined) {
                shooterName = cells[columnMap.fullName]?.textContent.trim();
            } else if (columnMap.lastName !== undefined && columnMap.firstName !== undefined) {
                const lastName = cells[columnMap.lastName]?.textContent.trim() || '';
                const firstName = cells[columnMap.firstName]?.textContent.trim() || '';
                shooterName = `${firstName} ${lastName}`.trim();
            }

            if (!shooterName) return null;

            return {
                shooter: shooterName,
                position: position
            };
        } catch (error) {
            console.error('Error extracting HTML row data:', error);
            return null;
        }
    }

    // Show paste import interface
    showPasteImport() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content" style="max-width: 700px;">
                <h3>📋 Paste Table Data</h3>
                <p>Copy the Kings Aggregate results from the NRAA website and paste below. You can paste either:</p>
                <ul style="margin: 10px 0; padding-left: 20px; font-size: 14px;">
                    <li><strong>Table data:</strong> Select and copy just the results table</li>
                    <li><strong>HTML source:</strong> Copy the entire page source (Ctrl+U → Ctrl+A → Ctrl+C)</li>
                </ul>
                
                <div style="margin-bottom: 15px;">
                    <label>Event Information:</label>
                    <div style="display: flex; gap: 10px; margin-top: 5px;">
                        <input type="date" id="paste-event-date" style="padding: 5px; border: 1px solid #ddd; border-radius: 4px;">
                        <select id="paste-event-location" style="padding: 5px; border: 1px solid #ddd; border-radius: 4px;">
                            <option value="">Select State</option>
                            <option value="ACT">ACT</option>
                            <option value="NSW">NSW</option>
                            <option value="NT">NT</option>
                            <option value="QLD">QLD</option>
                            <option value="SA">SA</option>
                            <option value="TAS">TAS</option>
                            <option value="VIC">VIC</option>
                            <option value="WA">WA</option>
                        </select>
                    </div>
                </div>
                
                <textarea id="paste-data" placeholder="Paste the copied table data here..." style="width: 100%; height: 300px; font-family: monospace; font-size: 12px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;"></textarea>
                
                <div style="margin-top: 15px; display: flex; gap: 10px;">
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" style="flex: 1; padding: 10px; background-color: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">Cancel</button>
                    <button onclick="manualImportHelper.generateSamplePasteData()" style="flex: 1; padding: 10px; background-color: #ff9800; color: white; border: none; border-radius: 4px; cursor: pointer;">Sample HTML</button>
                    <button onclick="manualImportHelper.processPastedData()" style="flex: 2; padding: 10px; background-color: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">Import Data</button>
                </div>
                
                <div style="margin-top: 10px; font-size: 12px; color: #666;">
                    <strong>💡 Tips:</strong>
                    <ul style="margin: 5px 0; padding-left: 15px;">
                        <li><strong>Table method:</strong> Find "Match XX - Aggregate - Kings" section, expand it, select the table and copy</li>
                        <li><strong>HTML method:</strong> Press Ctrl+U to view source, Ctrl+A to select all, Ctrl+C to copy, then paste here</li>
                        <li><strong>Look for:</strong> "Kings" aggregate sections (ignore "Grand Aggregate" and daily matches)</li>
                    </ul>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Set default date to today
        document.getElementById('paste-event-date').value = new Date().toISOString().split('T')[0];
    }

    // Process pasted data
    processPastedData() {
        const pastedText = document.getElementById('paste-data').value;
        const eventDate = document.getElementById('paste-event-date').value;
        const eventLocation = document.getElementById('paste-event-location').value;
        
        if (!pastedText.trim()) {
            alert('Please paste some table data first');
            return;
        }
        
        if (!eventLocation) {
            alert('Please select a state/location');
            return;
        }
        
        try {
            const eventData = this.parseTableData(pastedText, {
                date: eventDate,
                location: eventLocation
            });
            
            if (!eventData) {
                alert('Could not parse the pasted data. Please check the format and try again.');
                return;
            }
            
            // Validate and import
            const errors = rankingSystem.validateEventData(eventData);
            if (errors.length > 0) {
                alert('Data validation failed:\n\n' + errors.join('\n'));
                return;
            }
            
            // Import the event
            rankingSystem.addEvent(eventData);
            displayRankings();
            displayEvents();
            
            // Add to import history
            if (typeof addToImportHistory === 'function') {
                addToImportHistory(`Manual paste import: ${eventLocation} ${eventDate} (${eventData.results.length} shooters)`);
            }
            
            // Close modal
            document.querySelector('.modal-overlay').remove();
            
            alert(`Successfully imported ${eventData.results.length} shooters from ${eventLocation} ${eventDate}!`);
            
            // Switch to rankings tab
            switchTab('rankings');
            
        } catch (error) {
            console.error('Paste import failed:', error);
            alert('Import failed: ' + error.message);
        }
    }

    // Generate sample data for testing paste import
    generateSamplePasteData() {
        const sampleHtml = `
<div class="collapsible" style="margin-bottom: 5px;">
    <h2>Match 21 - Aggregate - Kings</h2>
</div>
<div class="content">
    <table>
        <tr>
            <th>Place</th>
            <th>Last Name</th>
            <th>Preferred Name</th>
            <th>Club</th>
            <th>State</th>
            <th>Score</th>
        </tr>
        <tr>
            <td>1</td>
            <td>Smith</td>
            <td>John</td>
            <td>Example Rifle Club</td>
            <td>NSW</td>
            <td>398.51</td>
        </tr>
        <tr>
            <td>2</td>
            <td>Johnson</td>
            <td>Sarah</td>
            <td>Test Shooting Club</td>
            <td>VIC</td>
            <td>397.48</td>
        </tr>
        <tr>
            <td>3</td>
            <td>Brown</td>
            <td>Michael</td>
            <td>Sample Club Inc.</td>
            <td>QLD</td>
            <td>396.39</td>
        </tr>
        <tr>
            <td>4</td>
            <td>Wilson</td>
            <td>Emma</td>
            <td>Demo Rifle Club</td>
            <td>SA</td>
            <td>395.29</td>
        </tr>
        <tr>
            <td>5</td>
            <td>Davis</td>
            <td>James</td>
            <td>Practice Club</td>
            <td>WA</td>
            <td>394.45</td>
        </tr>
    </table>
</div>`;

        document.getElementById('paste-data').value = sampleHtml;
    }
}

// Global instance
const manualImportHelper = new ManualImportHelper();

// Helper functions for UI
function showManualImportInstructions() {
    manualImportHelper.showManualImportInstructions();
}

function showAllNRAAUrls() {
    manualImportHelper.showAllUrls();
}

function showPasteImport() {
    manualImportHelper.showPasteImport();
}
